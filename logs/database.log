2025-07-14 16:30:33,031 WARNING database DDL Query made to DB:
ALTER TABLE `tabS<PERSON>ry Slip` MODIFY `base_year_to_date` decimal(21,9) not null default 0, MODIFY `income_tax_deducted_till_date` decimal(21,9) not null default 0, MODIFY `standard_tax_exemption_amount` decimal(21,9) not null default 0, MODIFY `tax_exemption_declaration` decimal(21,9) not null default 0, MODIFY `total_working_hours` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `absent_days` decimal(21,9) not null default 0, MODIFY `gross_pay` decimal(21,9) not null default 0, MODIFY `leave_without_pay` decimal(21,9) not null default 0, MODIFY `total_working_days` decimal(21,9) not null default 0, MODIFY `month_to_date` decimal(21,9) not null default 0, MODIFY `unmarked_days` decimal(21,9) not null default 0, MODIFY `payment_days` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_deduction` decimal(21,9) not null default 0, MODIFY `future_income_tax_deductions` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `deductions_before_tax_calculation` decimal(21,9) not null default 0, MODIFY `current_month_income_tax` decimal(21,9) not null default 0, MODIFY `annual_taxable_amount` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0, MODIFY `base_gross_year_to_date` decimal(21,9) not null default 0, MODIFY `base_total_deduction` decimal(21,9) not null default 0, MODIFY `base_net_pay` decimal(21,9) not null default 0, MODIFY `non_taxable_earnings` decimal(21,9) not null default 0, MODIFY `base_hour_rate` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `gross_year_to_date` decimal(21,9) not null default 0, MODIFY `base_month_to_date` decimal(21,9) not null default 0, MODIFY `total_income_tax` decimal(21,9) not null default 0, MODIFY `base_gross_pay` decimal(21,9) not null default 0, MODIFY `year_to_date` decimal(21,9) not null default 0, MODIFY `income_from_other_sources` decimal(21,9) not null default 0, MODIFY `total_earnings` decimal(21,9) not null default 0
2025-07-14 16:30:33,106 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` ADD COLUMN `create_cash_journal` int(1) not null default 0, ADD COLUMN `based_on_hourly_rate` int(1) not null default 0, ADD COLUMN `hourly_rate` decimal(21,9) not null default 0
2025-07-14 16:30:33,123 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `max_benefit_amount` decimal(21,9) not null default 0
2025-07-14 16:30:33,248 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` ADD COLUMN `pension_fund` varchar(140), ADD COLUMN `pension_fund_number` varchar(140), ADD COLUMN `wcf_number` varchar(140), ADD COLUMN `tin_number` varchar(140), ADD COLUMN `national_identity` varchar(140), ADD COLUMN `bank_code` varchar(140)
2025-07-14 16:30:33,269 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-07-14 16:30:33,522 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-07-14 16:30:33,713 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Item` ADD COLUMN `applicable_charges_per_item` decimal(21,9) not null default 0, ADD COLUMN `price_per_item` decimal(21,9) not null default 0
2025-07-14 16:30:33,728 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Item` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `applicable_charges` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-07-14 16:30:34,175 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `enable_auto_close_material_request` int(1) not null default 0, ADD COLUMN `close_material_request_after` int(11) not null default 0
2025-07-14 16:30:34,195 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0
2025-07-14 16:30:35,077 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `default_bank_charges_account` varchar(140)
2025-07-14 16:30:35,097 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0
2025-07-14 16:30:35,246 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` ADD COLUMN `bank_charges` decimal(21,9) not null default 0, ADD COLUMN `bank_charges_journal_entry` varchar(140), ADD COLUMN `bank_charges_description` text
2025-07-14 16:30:35,266 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0
2025-07-14 16:30:52,178 WARNING database DDL Query made to DB:
create table `tabDaily Checklist Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`checklist_task` varchar(140),
`remarks` text default 'NOT CHECKED',
`issue_photo` text,
`job_card` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:52,371 WARNING database DDL Query made to DB:
create table `tabOutsourcing Shift` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`shift_name` varchar(140) unique,
`outsourcing_category` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:52,610 WARNING database DDL Query made to DB:
create table `tabProperty` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`name1` varchar(140) unique,
`parent_property` varchar(140),
`is_group` int(1) not null default 0,
`photo` text,
`company` varchar(140),
`cost_center` varchar(140),
`unit_owner` varchar(140),
`title_deed_number` varchar(140),
`type` varchar(140),
`bedroom` int(11) not null default 0,
`master_bedroom` int(11) not null default 0,
`common_bathroom` int(11) not null default 0,
`builtup_area` decimal(21,9) not null default 0,
`carpet_area` decimal(21,9) not null default 0,
`facing` varchar(140),
`no_of_parking` int(11) not null default 0,
`rent` decimal(21,9) not null default 0,
`security_deposit` decimal(21,9) not null default 0,
`smoking` int(1) not null default 0,
`furnished` int(1) not null default 0,
`status` varchar(140),
`description` text,
`lft` int(11) not null default 0,
`rgt` int(11) not null default 0,
`old_parent` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:52,893 WARNING database DDL Query made to DB:
create table `tabInsurance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`insurance_provider` varchar(140),
`insurance_type` varchar(140),
`policy_number` varchar(140),
`premium_price` decimal(21,9) not null default 0,
`effective_date` date,
`expiration_date` date,
`description` varchar(140),
`cover_document` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:53,094 WARNING database DDL Query made to DB:
create table `tabProperty Unit` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`unit_number` varchar(140),
`name1` varchar(140),
`photo` text,
`unit_owner` varchar(140),
`title_deed_number` varchar(140),
`type` varchar(140),
`bedroom` int(11) not null default 0,
`master_bedroom` int(11) not null default 0,
`common_bathroom` int(11) not null default 0,
`builtup_area` decimal(21,9) not null default 0,
`carpet_area` decimal(21,9) not null default 0,
`facing` varchar(140),
`no_of_parking` int(11) not null default 0,
`rent` decimal(21,9) not null default 0,
`security_deposit` decimal(21,9) not null default 0,
`smoking` int(1) not null default 0,
`furnished` int(1) not null default 0,
`description` text,
`status_active` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:53,236 WARNING database DDL Query made to DB:
create table `tabSecurity Deposit Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`apartment_no` varchar(140),
`bhk` varchar(140),
`amount` decimal(21,9) not null default 0,
`remarks` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:53,402 WARNING database DDL Query made to DB:
create table `tabSecurity Attendance Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`guard_empid` varchar(140),
`guard_name` varchar(140),
`id_no` varchar(140),
`position` varchar(140),
`status` varchar(140),
`remarks` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:53,569 WARNING database DDL Query made to DB:
create table `tabLease Invoice Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`date_to_invoice` date,
`schedule_start_date` date,
`lease_item` varchar(140),
`paid_by` varchar(140),
`lease_item_name` varchar(140),
`document_type` varchar(140),
`invoice_number` varchar(140),
`sales_order_number` varchar(140),
`qty` decimal(21,9) not null default 0,
`rate` decimal(21,9) not null default 0,
`currency` varchar(140),
`tax` decimal(21,9) not null default 0,
`invoice_item_group` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:53,714 WARNING database DDL Query made to DB:
create table `tabSecurity Attendance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`shift_date` date,
`shift_name` varchar(140),
`amended_from` varchar(140),
`naming_series` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:53,912 WARNING database DDL Query made to DB:
create table `tabKey` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`key_number` varchar(140) unique,
`status_active` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:54,068 WARNING database DDL Query made to DB:
create table `tabTool Item Set` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`set_name` varchar(140),
`no_of_tool_items` int(11) not null default 0,
`shelf_no` varchar(140),
`location_no` varchar(140),
`status` varchar(140),
`duplicate_of` varchar(140),
`disabled` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `set_name`(`set_name`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:54,211 WARNING database DDL Query made to DB:
create table `tabCustom Error Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`seen` int(1) not null default 0,
`method` varchar(140),
`error` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=MyISAM
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:54,385 WARNING database DDL Query made to DB:
create table `tabMeter Reading` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`meter_type` varchar(140),
`reading_date` date,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:54,568 WARNING database DDL Query made to DB:
create table `tabChecklist Checkup Area` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`area_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:54,728 WARNING database DDL Query made to DB:
create table `tabDoor` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`door_type` varchar(140) unique,
`lock_type` varchar(140),
`screen_door_attached` int(1) not null default 0,
`inside_color` varchar(140),
`exterior_color` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:54,896 WARNING database DDL Query made to DB:
create table `tabLease Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lease_item` varchar(140),
`frequency` varchar(140),
`amount` decimal(21,9) not null default 0,
`currency_code` varchar(140) default 'USD',
`charge_basis` varchar(140),
`charge_rate` varchar(140),
`witholding_tax` decimal(21,9) not null default 0,
`paid_by` varchar(140),
`invoice_item_group` varchar(140),
`document_type` varchar(140) default 'Sales Invoice',
`valid_from` date,
`is_active` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:55,044 WARNING database DDL Query made to DB:
create table `tabOutsource Contact` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`full_name` varchar(140) unique,
`id_no` varchar(140),
`status` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:55,192 WARNING database DDL Query made to DB:
create table `tabApartment Status` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`apartment_no` varchar(140),
`bhk` varchar(140),
`rent` decimal(21,9) not null default 0,
`agreement_start_date` date,
`agreement_end_date` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:55,405 WARNING database DDL Query made to DB:
create table `tabDaily Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`checkup_date` date,
`shift` varchar(140),
`area` varchar(140),
`property` varchar(140),
`amended_from` varchar(140),
`naming_series` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:55,592 WARNING database DDL Query made to DB:
create table `tabProperty Amenity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`amenity_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:55,729 WARNING database DDL Query made to DB:
create table `tabChecklist Checkup Area Task` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`task_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:55,879 WARNING database DDL Query made to DB:
create table `tabMultiSelect Item Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_group` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:56,031 WARNING database DDL Query made to DB:
create table `tabMeter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`meter_number` varchar(140) unique,
`meter_type` varchar(140),
`initial_reading` varchar(140),
`status` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:56,186 WARNING database DDL Query made to DB:
create table `tabUnit Assets` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`asset` varchar(140),
`maintenable` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:56,342 WARNING database DDL Query made to DB:
create table `tabPaint` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` varchar(140),
`color_code` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:56,521 WARNING database DDL Query made to DB:
create table `tabUnit Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`unit_type` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:56,868 WARNING database DDL Query made to DB:
create table `tabLease` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`property` varchar(140),
`lease_date` date,
`lease_status` varchar(140),
`signed_agreement_received` int(1) not null default 0,
`stamp_duty_paid_by_tenant` int(1) not null default 0,
`property_owner` varchar(140),
`lease_customer` varchar(140),
`customer` varchar(140),
`property_user` varchar(140),
`start_date` date,
`skip_end_date` int(1) not null default 0,
`end_date` date,
`frequency` varchar(140),
`days_to_invoice_in_advance` int(11) not null default 0,
`notice_period` int(11) not null default 0,
`fit_out_period` int(11) not null default 0,
`security_deposit_currency` varchar(140) default 'USD',
`security_deposit` decimal(21,9) not null default 0,
`security_status` varchar(140),
`security_received_payment` varchar(140),
`security_returned_reference` varchar(140),
`late_payment_interest_percentage` decimal(21,9) not null default 0,
`wtax_paid_by` varchar(140),
`witness_2` varchar(140),
`witness_1` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:57,080 WARNING database DDL Query made to DB:
create table `tabOutsourcing Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`outsourcing_category` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:57,251 WARNING database DDL Query made to DB:
create table `tabExit` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lease` varchar(140),
`exit_type` varchar(140),
`premature_expiry_date` date,
`exit_details` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:57,426 WARNING database DDL Query made to DB:
create table `tabFlooring` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`flooring_type` varchar(140) unique,
`description` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:57,576 WARNING database DDL Query made to DB:
create table `tabProperty Meter Reading` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`meter_number` varchar(140),
`meter_type` varchar(140),
`installation_date` date,
`initial_meter_reading` decimal(21,9) not null default 0,
`invoice_customer` varchar(140),
`status` varchar(140),
`deactivation_date` date,
`property_number` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:57,739 WARNING database DDL Query made to DB:
create table `tabTool Item Record` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`tool_item_set` varchar(140),
`set_name` varchar(140),
`staff_type` varchar(140),
`taken_by` varchar(140),
`reason_for_tool_item_taken` varchar(140),
`datetime_taken` datetime(6),
`returned` int(1) not null default 0,
`return_date_and_time` datetime(6),
`other_remarks` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:57,907 WARNING database DDL Query made to DB:
create table `tabKey Set Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`key_set` varchar(140),
`set_name` varchar(140),
`staff_type` varchar(140),
`taken_by` varchar(140),
`reason_for_key_taken` varchar(140),
`datetime_taken` datetime(6),
`returned` int(1) not null default 0,
`return_date_and_time` datetime(6),
`other_remarks` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:58,055 WARNING database DDL Query made to DB:
create table `tabOutsourcing Attendance Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`person_name` varchar(140),
`position` varchar(140),
`status` varchar(140),
`remarks` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:58,250 WARNING database DDL Query made to DB:
create table `tabMeter Reading Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`property` varchar(140),
`meter_number` varchar(140),
`previous_meter_reading` decimal(21,9) not null default 0,
`current_meter_reading` decimal(21,9) not null default 0,
`reading_difference` decimal(21,9) not null default 0,
`previous_reading_date` date,
`do_not_create_invoice` int(1) not null default 0,
`invoice_number` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:58,387 WARNING database DDL Query made to DB:
create table `tabGuard Shift Location` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`location_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:58,546 WARNING database DDL Query made to DB:
create table `tabOutsourcing Attendance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`shift_date` date,
`shift_name` varchar(140),
`outsourcing_category_name` varchar(140),
`naming_series` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:58,730 WARNING database DDL Query made to DB:
create table `tabTool Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`tool_item_number` varchar(140) unique,
`status_active` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:58,895 WARNING database DDL Query made to DB:
create table `tabKey Set` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`set_name` varchar(140),
`no_of_keys` int(11) not null default 0,
`shelf_no` varchar(140),
`location_no` varchar(140),
`status` varchar(140),
`duplicate_of` varchar(140),
`disabled` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `set_name`(`set_name`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:59,066 WARNING database DDL Query made to DB:
create table `tabIssue Materials Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item` varchar(140),
`quantity` decimal(21,9) not null default 1.0,
`uom` varchar(140),
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`is_pos` int(1) not null default 0,
`material_status` varchar(140),
`material_request` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:59,213 WARNING database DDL Query made to DB:
create table `tabGuard Shift` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`shift_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:59,569 WARNING database DDL Query made to DB:
create table `tabIssue Materials Billed` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item` varchar(140),
`quantity` decimal(21,9) not null default 1.0,
`uom` varchar(140),
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`is_pos` int(1) not null default 0,
`material_status` varchar(140),
`invoiced` int(1) not null default 0,
`sales_invoice` varchar(140),
`stock_entry` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:30:59,703 WARNING database DDL Query made to DB:
create table `tabOutsourcing Shift Location` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`location_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:31:06,288 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request` ADD COLUMN `sales_invoice` varchar(140)
2025-07-14 16:31:06,305 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request` MODIFY `per_ordered` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0
2025-07-14 16:31:06,407 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation` ADD COLUMN `cost_center` varchar(140)
2025-07-14 16:31:06,424 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation` MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0
2025-07-14 16:31:06,530 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `security_account_code` varchar(140), ADD COLUMN `default_tax_account_head` varchar(140), ADD COLUMN `default_tax_template` varchar(140), ADD COLUMN `default_maintenance_tax_template` varchar(140)
2025-07-14 16:31:06,547 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0
2025-07-14 16:31:06,635 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue` ADD COLUMN `property_name` varchar(140), ADD COLUMN `person_in_charge` varchar(140), ADD COLUMN `sub_contractor_contact` varchar(140), ADD COLUMN `person_in_charge_name` varchar(140), ADD COLUMN `sub_contractor_name` varchar(140), ADD COLUMN `defect_found` longtext, ADD COLUMN `customer_feedback` longtext
2025-07-14 16:31:06,651 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue` MODIFY `first_response_time` decimal(21,9), MODIFY `resolution_time` decimal(21,9), MODIFY `total_hold_time` decimal(21,9), MODIFY `avg_response_time` decimal(21,9), MODIFY `user_resolution_time` decimal(21,9)
2025-07-14 16:31:06,745 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem` ADD COLUMN `reading_required` int(1) not null default 0
2025-07-14 16:31:06,762 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem` MODIFY `standard_rate` decimal(21,9) not null default 0, MODIFY `total_projected_qty` decimal(21,9) not null default 0, MODIFY `over_billing_allowance` decimal(21,9) not null default 0, MODIFY `opening_stock` decimal(21,9) not null default 0, MODIFY `witholding_tax_rate_on_purchase` decimal(21,9) not null default 0, MODIFY `safety_stock` decimal(21,9) not null default 0, MODIFY `max_discount` decimal(21,9) not null default 0, MODIFY `last_purchase_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate_on_sales` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `over_delivery_receipt_allowance` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0
2025-07-14 16:31:06,829 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue Materials Detail` ADD COLUMN `mateiral_request` varchar(140)
2025-07-14 16:31:06,845 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue Materials Detail` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-07-14 16:31:06,987 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `lease` varchar(140), ADD COLUMN `lease_item` varchar(140), ADD COLUMN `job_card` varchar(140)
2025-07-14 16:31:07,005 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0
2025-07-14 16:31:07,093 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request Item` ADD COLUMN `material_request` varchar(140)
2025-07-14 16:31:07,110 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request Item` MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `ordered_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `min_order_qty` decimal(21,9) not null default 0
2025-07-14 16:31:07,332 WARNING database DDL Query made to DB:
ALTER TABLE `tabProperty` ADD COLUMN `territory` varchar(140)
2025-07-14 16:31:07,349 WARNING database DDL Query made to DB:
ALTER TABLE `tabProperty` MODIFY `security_deposit` decimal(21,9) not null default 0, MODIFY `carpet_area` decimal(21,9) not null default 0, MODIFY `builtup_area` decimal(21,9) not null default 0, MODIFY `rent` decimal(21,9) not null default 0
2025-07-14 16:51:14,285 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-14 16:51:16,029 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-14 16:51:18,436 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-14 16:51:20,925 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-07-14 16:51:21,365 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-07-14 16:51:23,877 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-14 16:51:24,191 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-14 16:51:35,534 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` ADD COLUMN `custom_sales_invoice` varchar(140)
2025-07-14 16:51:35,555 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` MODIFY `total_incoming_value` decimal(21,9) not null default 0, MODIFY `total_additional_costs` decimal(21,9) not null default 0, MODIFY `value_difference` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `per_transferred` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `fg_completed_qty` decimal(21,9) not null default 0, MODIFY `total_outgoing_value` decimal(21,9) not null default 0, MODIFY `repack_qty` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0
2025-07-14 16:51:35,693 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `custom_uom` varchar(140), ADD COLUMN `custom_trade_in_item` varchar(140), ADD COLUMN `custom_trade_in_qty` decimal(21,9) not null default 0, ADD COLUMN `custom_trade_in_uom` varchar(140), ADD COLUMN `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, ADD COLUMN `custom_total_trade_in_value` decimal(21,9) not null default 0, ADD COLUMN `custom_trade_in_batch_no` varchar(140), ADD COLUMN `custom_trade_in_serial_no` text
2025-07-14 16:51:35,712 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0
2025-07-14 16:51:35,877 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `custom_is_trade_in` int(1) not null default 0
2025-07-14 16:51:35,895 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0
2025-07-14 16:51:36,037 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `custom_trade_in_sales_percentage` decimal(21,9) not null default 0, ADD COLUMN `custom_trade_in_control_account` varchar(140)
2025-07-14 16:51:36,054 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0
