2025-07-14 09:38:33,389 INFO ipython === bench console session ===
2025-07-14 09:38:33,389 INFO ipython import frappe
2025-07-14 09:38:33,389 INFO ipython frappe.get_all("DocType", filters={"module": "CV Manager"})
2025-07-14 09:38:33,390 INFO ipython from cv_maker.fixtures.test_data import create_test_data
2025-07-14 09:38:33,390 INFO ipython result = create_test_data()
2025-07-14 09:38:33,390 INFO ipython frappe.get_all("Company", limit=1)
2025-07-14 09:38:33,390 INFO ipython frappe.get_all("Employee", limit=1)
2025-07-14 09:38:33,390 INFO ipython template = frappe.new_doc("CV Template")
2025-07-14 09:38:33,391 INFO ipython template.template_name = "Test Template"
2025-07-14 09:38:33,391 INFO ipython template.template_html = "<h1>{{ employee_name }}</h1>"
2025-07-14 09:38:33,391 INFO ipython template.status = "Active"
2025-07-14 09:38:33,391 INFO ipython # First, let's create a Company
2025-07-14 09:38:33,391 INFO ipython company = frappe.new_doc("Company")
2025-07-14 09:38:33,392 INFO ipython company.company_name = "Mysite Co."
2025-07-14 09:38:33,392 INFO ipython company.abbr = "MSC"
2025-07-14 09:38:33,392 INFO ipython company.default_currency = "USD"
2025-07-14 09:38:33,392 INFO ipython company.insert(ignore_permissions=True)
2025-07-14 09:38:33,393 INFO ipython company.country = "United States"
2025-07-14 09:38:33,393 INFO ipython company.insert(ignore_permissions=True)
2025-07-14 09:38:33,393 INFO ipython frappe.get_all("Company")
2025-07-14 09:38:33,393 INFO ipython frappe.db.sql("SELECT name FROM `tabCompany`")
2025-07-14 09:38:33,393 INFO ipython frappe.db.commit()
2025-07-14 09:38:33,394 INFO ipython company = frappe.get_doc("Company", "Mysite Co.")
2025-07-14 09:38:33,394 INFO ipython # Create departments
2025-07-14 09:38:33,394 INFO ipython departments = ["Technology", "Product", "Design", "Analytics", "Human Resources"]
2025-07-14 09:38:33,394 INFO ipython for dept_name in departments:
        if not frappe.db.exists("Department", dept_name):
                    dept = frappe.new_doc("Department")
                            dept.department_name = dept_name
2025-07-14 09:38:33,394 INFO ipython         dept.company = "Mysite Co."
2025-07-14 09:38:33,395 INFO ipython         dept.insert(ignore_permissions=True)
2025-07-14 09:38:33,395 INFO ipython         print(f"Created department: {dept_name}")
2025-07-14 09:38:33,395 INFO ipython for dept_name in departments:
        if not frappe.db.exists("Department", dept_name):
                    dept = frappe.new_doc("Department")
                            dept.department_name = dept_name
2025-07-14 09:38:33,395 INFO ipython         dept.company = "Mysite Co."
2025-07-14 09:38:33,395 INFO ipython         dept.insert(ignore_permissions=True)
2025-07-14 09:38:33,396 INFO ipython         print(f"Created department: {dept_name}")
2025-07-14 09:38:33,396 INFO ipython === session end ===
2025-07-14 09:49:47,231 INFO ipython === bench console session ===
2025-07-14 09:49:47,231 INFO ipython import frappe
2025-07-14 09:49:47,232 INFO ipython # Clear existing CV profiles and child table data
2025-07-14 09:49:47,232 INFO ipython frappe.db.sql("DELETE FROM `tabEmployee CV Profile`")
2025-07-14 09:49:47,232 INFO ipython frappe.db.sql("DELETE FROM `tabCV Skill`")
2025-07-14 09:49:47,232 INFO ipython frappe.db.sql("DELETE FROM `tabCV Language`")
2025-07-14 09:49:47,233 INFO ipython frappe.db.sql("DELETE FROM `tabCV Project`")
2025-07-14 09:49:47,233 INFO ipython frappe.db.sql("DELETE FROM `tabCV Certification`")
2025-07-14 09:49:47,233 INFO ipython frappe.db.commit()
2025-07-14 09:49:47,233 INFO ipython === session end ===
2025-07-14 16:15:16,347 INFO ipython === bench console session ===
2025-07-14 16:15:16,356 INFO ipython import frappe
2025-07-14 16:15:16,357 INFO ipython # Test the export functionality
2025-07-14 16:15:16,357 INFO ipython from cv_maker.api import export_cv_profile
2025-07-14 16:15:16,357 INFO ipython # Get the first CV profile
2025-07-14 16:15:16,358 INFO ipython cv_profiles = frappe.get_all("Employee CV Profile", limit=1)
2025-07-14 16:15:16,358 INFO ipython print(cv_profiles)
2025-07-14 16:15:16,358 INFO ipython # Test PDF export
2025-07-14 16:15:16,358 INFO ipython result = export_cv_profile("CV-HR-EMP-00003", "PDF")
2025-07-14 16:15:16,359 INFO ipython print("PDF Export Result:", result)
2025-07-14 16:15:16,359 INFO ipython # Test DOCX export
2025-07-14 16:15:16,359 INFO ipython docx_result = export_cv_profile("CV-HR-EMP-00003", "DOCX")
2025-07-14 16:15:16,359 INFO ipython print("DOCX Export successful! Length:", len(docx_result))
2025-07-14 16:15:16,360 INFO ipython # Test HTML export
2025-07-14 16:15:16,360 INFO ipython html_result = export_cv_profile("CV-HR-EMP-00003", "HTML")
2025-07-14 16:15:16,360 INFO ipython print("HTML Export successful! Length:", len(html_result))
2025-07-14 16:15:16,360 INFO ipython # Test public link generation
2025-07-14 16:15:16,361 INFO ipython from cv_maker.api import generate_public_link
2025-07-14 16:15:16,361 INFO ipython public_link = generate_public_link("CV-HR-EMP-00003", 30)
2025-07-14 16:15:16,361 INFO ipython print("Public link generated:", public_link)
2025-07-14 16:15:16,361 INFO ipython === session end ===
