2025-07-14 09:01:20,455 WARNING database DDL Query made to DB:
create table `tabProcess Statement Of Accounts` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`report` varchar(140),
`from_date` date,
`posting_date` date,
`company` varchar(140),
`account` varchar(140),
`categorize_by` varchar(140) default 'Categorize by Voucher (Consolidated)',
`territory` varchar(140),
`ignore_exchange_rate_revaluation_journals` int(1) not null default 0,
`ignore_cr_dr_notes` int(1) not null default 0,
`to_date` date,
`finance_book` varchar(140),
`currency` varchar(140),
`payment_terms_template` varchar(140),
`sales_partner` varchar(140),
`sales_person` varchar(140),
`show_remarks` int(1) not null default 0,
`based_on_payment_terms` int(1) not null default 0,
`customer_collection` varchar(140),
`collection_name` varchar(140),
`primary_mandatory` int(1) not null default 1,
`show_net_values_in_party_account` int(1) not null default 0,
`orientation` varchar(140),
`include_break` int(1) not null default 1,
`include_ageing` int(1) not null default 0,
`ageing_based_on` varchar(140) default 'Due Date',
`letter_head` varchar(140),
`terms_and_conditions` varchar(140),
`enable_auto_email` int(1) not null default 0,
`sender` varchar(140),
`frequency` varchar(140),
`filter_duration` int(11) not null default 1,
`start_date` date,
`pdf_name` varchar(140),
`subject` varchar(140),
`body` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:23,097 WARNING database DDL Query made to DB:
create table `tabCompetitor Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`competitor` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:23,206 WARNING database DDL Query made to DB:
create table `tabOpportunity Lost Reason Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lost_reason` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:23,314 WARNING database DDL Query made to DB:
create table `tabContract Template Fulfilment Terms` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`requirement` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:23,600 WARNING database DDL Query made to DB:
create table `tabAppointment Booking Slots` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`day_of_week` varchar(140),
`from_time` time(6),
`to_time` time(6),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:23,751 WARNING database DDL Query made to DB:
create table `tabContract Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) unique,
`contract_terms` longtext,
`requires_fulfilment` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:23,945 WARNING database DDL Query made to DB:
create table `tabOpportunity Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`uom` varchar(140),
`qty` decimal(21,9) not null default 1.0,
`brand` varchar(140),
`item_group` varchar(140),
`description` longtext,
`image` text,
`base_rate` decimal(21,9) not null default 0,
`base_amount` decimal(21,9) not null default 0,
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:24,052 WARNING database DDL Query made to DB:
create table `tabOpportunity Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:24,373 WARNING database DDL Query made to DB:
create table `tabOpportunity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`opportunity_from` varchar(140),
`party_name` varchar(140),
`customer_name` varchar(140),
`status` varchar(140) default 'Open',
`opportunity_type` varchar(140) default 'Sales',
`source` varchar(140),
`opportunity_owner` varchar(140),
`sales_stage` varchar(140) default 'Prospecting',
`expected_closing` date,
`probability` decimal(21,9) not null default 100.0,
`no_of_employees` varchar(140),
`annual_revenue` decimal(21,9) not null default 0,
`customer_group` varchar(140),
`industry` varchar(140),
`market_segment` varchar(140),
`website` varchar(140),
`city` varchar(140),
`state` varchar(140),
`country` varchar(140),
`territory` varchar(140),
`currency` varchar(140),
`conversion_rate` decimal(21,9) not null default 0,
`opportunity_amount` decimal(21,9) not null default 0,
`base_opportunity_amount` decimal(21,9) not null default 0,
`company` varchar(140),
`campaign` varchar(140),
`transaction_date` date,
`language` varchar(140),
`amended_from` varchar(140),
`title` varchar(140),
`first_response_time` decimal(21,9),
`order_lost_reason` text,
`contact_person` varchar(140),
`job_title` varchar(140),
`contact_email` varchar(140),
`contact_mobile` varchar(140),
`whatsapp` varchar(140),
`phone` varchar(140),
`phone_ext` varchar(140),
`customer_address` varchar(140),
`address_display` text,
`contact_display` text,
`base_total` decimal(21,9) not null default 0,
`total` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `customer_group`(`customer_group`),
index `territory`(`territory`),
index `company`(`company`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:24,652 WARNING database DDL Query made to DB:
create table `tabContract Fulfilment Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`fulfilled` int(1) not null default 0,
`requirement` varchar(140),
`notes` text,
`amended_from` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:24,782 WARNING database DDL Query made to DB:
create table `tabLead Source` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`source_name` varchar(140) unique,
`details` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:24,909 WARNING database DDL Query made to DB:
create table `tabCompetitor` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`competitor_name` varchar(140) unique,
`website` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:25,202 WARNING database DDL Query made to DB:
create table `tabLead` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`salutation` varchar(140),
`first_name` varchar(140),
`middle_name` varchar(140),
`last_name` varchar(140),
`lead_name` varchar(140),
`job_title` varchar(140),
`gender` varchar(140),
`source` varchar(140),
`lead_owner` varchar(140),
`status` varchar(140) default 'Lead',
`customer` varchar(140),
`type` varchar(140),
`request_type` varchar(140),
`email_id` varchar(140),
`website` varchar(140),
`mobile_no` varchar(140),
`whatsapp_no` varchar(140),
`phone` varchar(140),
`phone_ext` varchar(140),
`company_name` varchar(140),
`no_of_employees` varchar(140),
`annual_revenue` decimal(21,9) not null default 0,
`industry` varchar(140),
`market_segment` varchar(140),
`territory` varchar(140),
`fax` varchar(140),
`city` varchar(140),
`state` varchar(140),
`country` varchar(140),
`qualification_status` varchar(140),
`qualified_by` varchar(140),
`qualified_on` date,
`campaign_name` varchar(140),
`company` varchar(140),
`language` varchar(140),
`image` text,
`title` varchar(140),
`disabled` int(1) not null default 0,
`unsubscribed` int(1) not null default 0,
`blog_subscriber` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `lead_name`(`lead_name`),
index `lead_owner`(`lead_owner`),
index `status`(`status`),
index `email_id`(`email_id`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:25,351 WARNING database DDL Query made to DB:
create table `tabOpportunity Lost Reason` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lost_reason` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:25,484 WARNING database DDL Query made to DB:
create table `tabLost Reason Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lost_reason` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:25,610 WARNING database DDL Query made to DB:
create table `tabAvailability Of Slots` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`day_of_week` varchar(140),
`from_time` time(6),
`to_time` time(6),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:25,763 WARNING database DDL Query made to DB:
create sequence if not exists prospect_opportunity_id_seq nocache nocycle
2025-07-14 09:01:25,782 WARNING database DDL Query made to DB:
create table `tabProspect Opportunity` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`opportunity` varchar(140),
`amount` decimal(21,9) not null default 0,
`stage` varchar(140),
`deal_owner` varchar(140),
`probability` decimal(21,9) not null default 0,
`expected_closing` date,
`currency` varchar(140),
`contact_person` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:25,907 WARNING database DDL Query made to DB:
create table `tabEmail Campaign` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`campaign_name` varchar(140),
`email_campaign_for` varchar(140) default 'Lead',
`recipient` varchar(140),
`sender` varchar(140),
`start_date` date,
`end_date` date,
`status` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:26,179 WARNING database DDL Query made to DB:
create table `tabAppointment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`scheduled_time` datetime(6),
`status` varchar(140),
`customer_name` varchar(140),
`customer_phone_number` varchar(140),
`customer_skype` varchar(140),
`customer_email` varchar(140),
`customer_details` longtext,
`appointment_with` varchar(140),
`party` varchar(140),
`calendar_event` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:26,337 WARNING database DDL Query made to DB:
create table `tabMarket Segment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`market_segment` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:26,561 WARNING database DDL Query made to DB:
create table `tabContract` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`party_type` varchar(140) default 'Customer',
`is_signed` int(1) not null default 0,
`party_name` varchar(140),
`party_user` varchar(140),
`status` varchar(140),
`fulfilment_status` varchar(140),
`party_full_name` varchar(140),
`start_date` date,
`end_date` date,
`signee` varchar(140),
`signed_on` datetime(6),
`ip_address` varchar(140),
`contract_template` varchar(140),
`contract_terms` longtext,
`requires_fulfilment` int(1) not null default 0,
`fulfilment_deadline` date,
`signee_company` longtext,
`signed_by_company` varchar(140),
`document_type` varchar(140),
`document_name` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:26,715 WARNING database DDL Query made to DB:
create table `tabCampaign Email Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`email_template` varchar(140),
`send_after_days` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:26,863 WARNING database DDL Query made to DB:
create sequence if not exists crm_note_id_seq nocache nocycle
2025-07-14 09:01:26,885 WARNING database DDL Query made to DB:
create table `tabCRM Note` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`note` longtext,
`added_by` varchar(140),
`added_on` datetime(6),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:27,054 WARNING database DDL Query made to DB:
create table `tabCampaign` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`campaign_name` varchar(140),
`naming_series` varchar(140),
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:27,202 WARNING database DDL Query made to DB:
create table `tabSales Stage` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`stage_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:27,438 WARNING database DDL Query made to DB:
create table `tabProspect` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company_name` varchar(140) unique,
`customer_group` varchar(140),
`no_of_employees` varchar(140),
`annual_revenue` decimal(21,9) not null default 0,
`market_segment` varchar(140),
`industry` varchar(140),
`territory` varchar(140),
`prospect_owner` varchar(140),
`website` varchar(140),
`fax` varchar(140),
`company` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:27,620 WARNING database DDL Query made to DB:
create table `tabProspect Lead` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lead` varchar(140),
`lead_name` varchar(140),
`email` varchar(140),
`mobile_no` varchar(140),
`lead_owner` varchar(140),
`status` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:28,076 WARNING database DDL Query made to DB:
create table `tabSupplier Scorecard Standing` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`standing_name` varchar(140) unique,
`standing_color` varchar(140),
`min_grade` decimal(21,9) not null default 0,
`max_grade` decimal(21,9) not null default 0,
`warn_rfqs` int(1) not null default 0,
`warn_pos` int(1) not null default 0,
`prevent_rfqs` int(1) not null default 0,
`prevent_pos` int(1) not null default 0,
`notify_supplier` int(1) not null default 0,
`notify_employee` int(1) not null default 0,
`employee_link` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:28,231 WARNING database DDL Query made to DB:
create table `tabSupplier Scorecard Scoring Standing` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`standing_name` varchar(140),
`standing_color` varchar(140),
`min_grade` decimal(21,9) not null default 0,
`max_grade` decimal(21,9) not null default 0,
`warn_rfqs` int(1) not null default 0,
`warn_pos` int(1) not null default 0,
`prevent_rfqs` int(1) not null default 0,
`prevent_pos` int(1) not null default 0,
`notify_supplier` int(1) not null default 0,
`notify_employee` int(1) not null default 0,
`employee_link` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:28,415 WARNING database DDL Query made to DB:
create table `tabRequest for Quotation Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`supplier_part_no` varchar(140),
`item_name` varchar(140),
`schedule_date` date,
`description` longtext,
`item_group` varchar(140),
`brand` varchar(140),
`image` text,
`qty` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`stock_qty` decimal(21,9) not null default 0,
`warehouse` varchar(140),
`material_request` varchar(140),
`material_request_item` varchar(140),
`project_name` varchar(140),
`page_break` int(1) not null default 0,
index `item_code`(`item_code`),
index `item_name`(`item_name`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:28,578 WARNING database DDL Query made to DB:
create table `tabPurchase Order Item Supplied` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`main_item_code` varchar(140),
`rm_item_code` varchar(140),
`stock_uom` varchar(140),
`reserve_warehouse` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`bom_detail_no` varchar(140),
`reference_name` varchar(140),
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`required_qty` decimal(21,9) not null default 0,
`supplied_qty` decimal(21,9) not null default 0,
`consumed_qty` decimal(21,9) not null default 0,
`returned_qty` decimal(21,9) not null default 0,
`total_supplied_qty` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:28,706 WARNING database DDL Query made to DB:
create table `tabSupplier Scorecard Period` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`supplier` varchar(140),
`naming_series` varchar(140),
`total_score` decimal(21,9) not null default 0,
`start_date` date,
`end_date` date,
`scorecard` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:28,862 WARNING database DDL Query made to DB:
create table `tabSupplier Scorecard Scoring Criteria` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`criteria_name` varchar(140),
`score` decimal(21,9) not null default 0,
`weight` decimal(21,9) not null default 0,
`max_score` decimal(21,9) not null default 100.0,
`formula` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:28,975 WARNING database DDL Query made to DB:
create table `tabSupplier Scorecard Scoring Variable` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`variable_label` varchar(140),
`description` text,
`value` decimal(21,9) not null default 0,
`param_name` varchar(140),
`path` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:29,369 WARNING database DDL Query made to DB:
create table `tabSupplier Quotation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) default '{supplier_name}',
`naming_series` varchar(140),
`supplier` varchar(140),
`supplier_name` varchar(140),
`company` varchar(140),
`status` varchar(140),
`transaction_date` date,
`valid_till` date,
`quotation_number` varchar(140),
`has_unit_price_items` int(1) not null default 0,
`amended_from` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`currency` varchar(140),
`conversion_rate` decimal(21,9) not null default 0,
`buying_price_list` varchar(140),
`price_list_currency` varchar(140),
`plc_conversion_rate` decimal(21,9) not null default 0,
`ignore_pricing_rule` int(1) not null default 0,
`total_qty` decimal(21,9) not null default 0,
`total_net_weight` decimal(21,9) not null default 0,
`base_total` decimal(21,9) not null default 0,
`base_net_total` decimal(21,9) not null default 0,
`total` decimal(21,9) not null default 0,
`net_total` decimal(21,9) not null default 0,
`tax_category` varchar(140),
`taxes_and_charges` varchar(140),
`shipping_rule` varchar(140),
`incoterm` varchar(140),
`named_place` varchar(140),
`base_taxes_and_charges_added` decimal(21,9) not null default 0,
`base_taxes_and_charges_deducted` decimal(21,9) not null default 0,
`base_total_taxes_and_charges` decimal(21,9) not null default 0,
`taxes_and_charges_added` decimal(21,9) not null default 0,
`taxes_and_charges_deducted` decimal(21,9) not null default 0,
`total_taxes_and_charges` decimal(21,9) not null default 0,
`apply_discount_on` varchar(140) default 'Grand Total',
`base_discount_amount` decimal(21,9) not null default 0,
`additional_discount_percentage` decimal(21,9) not null default 0,
`discount_amount` decimal(21,9) not null default 0,
`base_grand_total` decimal(21,9) not null default 0,
`base_rounding_adjustment` decimal(21,9) not null default 0,
`base_rounded_total` decimal(21,9) not null default 0,
`base_in_words` varchar(240),
`grand_total` decimal(21,9) not null default 0,
`rounding_adjustment` decimal(21,9) not null default 0,
`rounded_total` decimal(21,9) not null default 0,
`in_words` varchar(240),
`disable_rounded_total` int(1) not null default 0,
`other_charges_calculation` longtext,
`supplier_address` varchar(140),
`address_display` text,
`contact_person` varchar(140),
`contact_display` text,
`contact_mobile` text,
`contact_email` varchar(140),
`shipping_address` varchar(140),
`shipping_address_display` text,
`billing_address` varchar(140),
`billing_address_display` text,
`tc_name` varchar(140),
`terms` longtext,
`letter_head` varchar(140),
`group_same_items` int(1) not null default 0,
`select_print_heading` varchar(140),
`language` varchar(140),
`auto_repeat` varchar(140),
`is_subcontracted` int(1) not null default 0,
`opportunity` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `supplier`(`supplier`),
index `company`(`company`),
index `status`(`status`),
index `transaction_date`(`transaction_date`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:29,552 WARNING database DDL Query made to DB:
create table `tabRequest for Quotation Supplier` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`supplier` varchar(140),
`contact` varchar(140),
`quote_status` varchar(140),
`supplier_name` varchar(140),
`email_id` varchar(140),
`send_email` int(1) not null default 1,
`email_sent` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:29,712 WARNING database DDL Query made to DB:
create table `tabSupplier Scorecard` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`supplier` varchar(140),
`supplier_score` varchar(140),
`indicator_color` varchar(140),
`status` varchar(140),
`period` varchar(140) default 'Per Month',
`weighting_function` text default '{total_score} * max( 0, min ( 1 , (12 - {period_number}) / 12) )',
`warn_rfqs` int(1) not null default 0,
`warn_pos` int(1) not null default 0,
`prevent_rfqs` int(1) not null default 0,
`prevent_pos` int(1) not null default 0,
`notify_supplier` int(1) not null default 0,
`notify_employee` int(1) not null default 0,
`employee` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:29,996 WARNING database DDL Query made to DB:
create table `tabSupplier Quotation Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`supplier_part_no` varchar(140),
`item_name` varchar(140),
`lead_time_days` int(11) not null default 0,
`expected_delivery_date` date,
`is_free_item` int(1) not null default 0,
`description` longtext,
`item_group` varchar(140),
`brand` varchar(140),
`image` text,
`qty` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`stock_qty` decimal(21,9) not null default 0,
`price_list_rate` decimal(21,9) not null default 0,
`discount_percentage` decimal(21,9) not null default 0,
`discount_amount` decimal(21,9) not null default 0,
`distributed_discount_amount` decimal(21,9) not null default 0,
`base_price_list_rate` decimal(21,9) not null default 0,
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`item_tax_template` varchar(140),
`base_rate` decimal(21,9) not null default 0,
`base_amount` decimal(21,9) not null default 0,
`pricing_rules` text,
`net_rate` decimal(21,9) not null default 0,
`net_amount` decimal(21,9) not null default 0,
`base_net_rate` decimal(21,9) not null default 0,
`base_net_amount` decimal(21,9) not null default 0,
`weight_per_unit` decimal(21,9) not null default 0,
`total_weight` decimal(21,9) not null default 0,
`weight_uom` varchar(140),
`warehouse` varchar(140),
`prevdoc_doctype` varchar(140),
`material_request` varchar(140),
`sales_order` varchar(140),
`request_for_quotation` varchar(140),
`material_request_item` varchar(140),
`request_for_quotation_item` varchar(140),
`item_tax_rate` longtext,
`manufacturer` varchar(140),
`manufacturer_part_no` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`page_break` int(1) not null default 0,
index `item_code`(`item_code`),
index `item_name`(`item_name`),
index `material_request`(`material_request`),
index `sales_order`(`sales_order`),
index `material_request_item`(`material_request_item`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:30,289 WARNING database DDL Query made to DB:
create table `tabSupplier` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`supplier_name` varchar(140),
`country` varchar(140),
`supplier_group` varchar(140),
`supplier_type` varchar(140) default 'Company',
`is_transporter` int(1) not null default 0,
`image` text,
`default_currency` varchar(140),
`default_bank_account` varchar(140),
`default_price_list` varchar(140),
`is_internal_supplier` int(1) not null default 0,
`represents_company` varchar(140),
`supplier_details` text,
`website` varchar(140),
`language` varchar(140),
`tax_id` varchar(140),
`tax_category` varchar(140),
`tax_withholding_category` varchar(140),
`supplier_primary_address` varchar(140),
`primary_address` text,
`supplier_primary_contact` varchar(140),
`mobile_no` varchar(140),
`email_id` varchar(140),
`payment_terms` varchar(140),
`allow_purchase_invoice_creation_without_purchase_order` int(1) not null default 0,
`allow_purchase_invoice_creation_without_purchase_receipt` int(1) not null default 0,
`is_frozen` int(1) not null default 0,
`disabled` int(1) not null default 0,
`warn_rfqs` int(1) not null default 0,
`warn_pos` int(1) not null default 0,
`prevent_rfqs` int(1) not null default 0,
`prevent_pos` int(1) not null default 0,
`on_hold` int(1) not null default 0,
`hold_type` varchar(140),
`release_date` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:30,660 WARNING database DDL Query made to DB:
create table `tabPurchase Order Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`fg_item` varchar(140),
`fg_item_qty` decimal(21,9) not null default 1.0,
`item_code` varchar(140),
`supplier_part_no` varchar(140),
`item_name` varchar(140),
`brand` varchar(140),
`product_bundle` varchar(140),
`schedule_date` date,
`expected_delivery_date` date,
`item_group` varchar(140),
`description` longtext,
`image` text,
`qty` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`subcontracted_quantity` decimal(21,9) not null default 0,
`uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`stock_qty` decimal(21,9) not null default 0,
`price_list_rate` decimal(21,9) not null default 0,
`last_purchase_rate` decimal(21,9) not null default 0,
`base_price_list_rate` decimal(21,9) not null default 0,
`margin_type` varchar(140),
`margin_rate_or_amount` decimal(21,9) not null default 0,
`rate_with_margin` decimal(21,9) not null default 0,
`discount_percentage` decimal(21,9) not null default 0,
`discount_amount` decimal(21,9) not null default 0,
`distributed_discount_amount` decimal(21,9) not null default 0,
`base_rate_with_margin` decimal(21,9) not null default 0,
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`item_tax_template` varchar(140),
`base_rate` decimal(21,9) not null default 0,
`base_amount` decimal(21,9) not null default 0,
`pricing_rules` text,
`stock_uom_rate` decimal(21,9) not null default 0,
`is_free_item` int(1) not null default 0,
`apply_tds` int(1) not null default 1,
`net_rate` decimal(21,9) not null default 0,
`net_amount` decimal(21,9) not null default 0,
`base_net_rate` decimal(21,9) not null default 0,
`base_net_amount` decimal(21,9) not null default 0,
`from_warehouse` varchar(140),
`warehouse` varchar(140),
`actual_qty` decimal(21,9) not null default 0,
`company_total_stock` decimal(21,9) not null default 0,
`material_request` varchar(140),
`material_request_item` varchar(140),
`sales_order` varchar(140),
`sales_order_item` varchar(140),
`sales_order_packed_item` varchar(140),
`supplier_quotation` varchar(140),
`supplier_quotation_item` varchar(140),
`delivered_by_supplier` int(1) not null default 0,
`against_blanket_order` int(1) not null default 0,
`blanket_order` varchar(140),
`blanket_order_rate` decimal(21,9) not null default 0,
`received_qty` decimal(21,9) not null default 0,
`returned_qty` decimal(21,9) not null default 0,
`billed_amt` decimal(21,9) not null default 0,
`expense_account` varchar(140),
`wip_composite_asset` varchar(140),
`manufacturer` varchar(140),
`manufacturer_part_no` varchar(140),
`bom` varchar(140),
`include_exploded_items` int(1) not null default 0,
`weight_per_unit` decimal(21,9) not null default 0,
`total_weight` decimal(21,9) not null default 0,
`weight_uom` varchar(140),
`project` varchar(140),
`cost_center` varchar(140),
`is_fixed_asset` int(1) not null default 0,
`item_tax_rate` longtext,
`production_plan` varchar(140),
`production_plan_item` varchar(140),
`production_plan_sub_assembly_item` varchar(140),
`page_break` int(1) not null default 0,
index `expected_delivery_date`(`expected_delivery_date`),
index `material_request`(`material_request`),
index `material_request_item`(`material_request_item`),
index `sales_order`(`sales_order`),
index `sales_order_item`(`sales_order_item`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:30,715 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order Item`
				ADD INDEX IF NOT EXISTS `item_code_warehouse_index`(item_code, warehouse)
2025-07-14 09:01:30,830 WARNING database DDL Query made to DB:
create table `tabSupplier Scorecard Variable` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`variable_label` varchar(140) unique,
`is_custom` int(1) not null default 0,
`param_name` varchar(140) unique,
`path` varchar(140),
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:31,575 WARNING database DDL Query made to DB:
create table `tabPurchase Order` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) default '{supplier_name}',
`naming_series` varchar(140),
`supplier` varchar(140),
`supplier_name` varchar(140),
`order_confirmation_no` varchar(140),
`order_confirmation_date` date,
`transaction_date` date,
`schedule_date` date,
`company` varchar(140),
`apply_tds` int(1) not null default 0,
`tax_withholding_category` varchar(140),
`is_subcontracted` int(1) not null default 0,
`has_unit_price_items` int(1) not null default 0,
`supplier_warehouse` varchar(140),
`amended_from` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`currency` varchar(140),
`conversion_rate` decimal(21,9) not null default 0,
`buying_price_list` varchar(140),
`price_list_currency` varchar(140),
`plc_conversion_rate` decimal(21,9) not null default 0,
`ignore_pricing_rule` int(1) not null default 0,
`scan_barcode` varchar(140),
`set_from_warehouse` varchar(140),
`set_warehouse` varchar(140),
`total_qty` decimal(21,9) not null default 0,
`total_net_weight` decimal(21,9) not null default 0,
`base_total` decimal(21,9) not null default 0,
`base_net_total` decimal(21,9) not null default 0,
`total` decimal(21,9) not null default 0,
`net_total` decimal(21,9) not null default 0,
`tax_withholding_net_total` decimal(21,9) not null default 0,
`base_tax_withholding_net_total` decimal(21,9) not null default 0,
`set_reserve_warehouse` varchar(140),
`tax_category` varchar(140),
`taxes_and_charges` varchar(140),
`shipping_rule` varchar(140),
`incoterm` varchar(140),
`named_place` varchar(140),
`base_taxes_and_charges_added` decimal(21,9) not null default 0,
`base_taxes_and_charges_deducted` decimal(21,9) not null default 0,
`base_total_taxes_and_charges` decimal(21,9) not null default 0,
`taxes_and_charges_added` decimal(21,9) not null default 0,
`taxes_and_charges_deducted` decimal(21,9) not null default 0,
`total_taxes_and_charges` decimal(21,9) not null default 0,
`base_grand_total` decimal(21,9) not null default 0,
`base_rounding_adjustment` decimal(21,9) not null default 0,
`base_in_words` varchar(240),
`base_rounded_total` decimal(21,9) not null default 0,
`grand_total` decimal(21,9) not null default 0,
`rounding_adjustment` decimal(21,9) not null default 0,
`rounded_total` decimal(21,9) not null default 0,
`disable_rounded_total` int(1) not null default 0,
`in_words` varchar(240),
`advance_paid` decimal(21,9) not null default 0,
`apply_discount_on` varchar(140) default 'Grand Total',
`base_discount_amount` decimal(21,9) not null default 0,
`additional_discount_percentage` decimal(21,9) not null default 0,
`discount_amount` decimal(21,9) not null default 0,
`other_charges_calculation` longtext,
`supplier_address` varchar(140),
`address_display` text,
`contact_person` varchar(140),
`contact_display` text,
`contact_mobile` text,
`contact_email` text,
`dispatch_address` varchar(140),
`dispatch_address_display` longtext,
`shipping_address` varchar(140),
`shipping_address_display` text,
`billing_address` varchar(140),
`billing_address_display` text,
`customer` varchar(140),
`customer_name` varchar(140),
`customer_contact_person` varchar(140),
`customer_contact_display` text,
`customer_contact_mobile` text,
`customer_contact_email` longtext,
`payment_terms_template` varchar(140),
`tc_name` varchar(140),
`terms` longtext,
`status` varchar(140) default 'Draft',
`per_billed` decimal(21,9) not null default 0,
`per_received` decimal(21,9) not null default 0,
`letter_head` varchar(140),
`group_same_items` int(1) not null default 0,
`select_print_heading` varchar(140),
`language` varchar(140),
`from_date` date,
`to_date` date,
`auto_repeat` varchar(140),
`is_internal_supplier` int(1) not null default 0,
`represents_company` varchar(140),
`ref_sq` varchar(140),
`party_account_currency` varchar(140),
`inter_company_order_reference` varchar(140),
`is_old_subcontracting_flow` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `supplier`(`supplier`),
index `transaction_date`(`transaction_date`),
index `status`(`status`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:31,871 WARNING database DDL Query made to DB:
create table `tabRequest for Quotation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`company` varchar(140),
`billing_address` varchar(140),
`billing_address_display` text,
`vendor` varchar(140),
`transaction_date` date,
`schedule_date` date,
`status` varchar(140),
`has_unit_price_items` int(1) not null default 0,
`amended_from` varchar(140),
`email_template` varchar(140),
`send_attached_files` int(1) not null default 1,
`send_document_print` int(1) not null default 0,
`message_for_supplier` longtext,
`incoterm` varchar(140),
`named_place` varchar(140),
`tc_name` varchar(140),
`terms` longtext,
`select_print_heading` varchar(140),
`letter_head` varchar(140),
`opportunity` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `company`(`company`),
index `transaction_date`(`transaction_date`),
index `status`(`status`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:32,048 WARNING database DDL Query made to DB:
create table `tabSupplier Scorecard Criteria` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`criteria_name` varchar(140) unique,
`max_score` decimal(21,9) not null default 100.0,
`formula` text,
`weight` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:32,232 WARNING database DDL Query made to DB:
create table `tabPurchase Receipt Item Supplied` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`main_item_code` varchar(140),
`rm_item_code` varchar(140),
`item_name` varchar(140),
`bom_detail_no` varchar(140),
`description` longtext,
`stock_uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`reference_name` varchar(140),
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`required_qty` decimal(21,9) not null default 0,
`consumed_qty` decimal(21,9) not null default 0,
`current_stock` decimal(21,9) not null default 0,
`batch_no` varchar(140),
`serial_no` text,
`purchase_order` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:32,791 WARNING database DDL Query made to DB:
create table `tabProject User` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`email` varchar(140),
`image` varchar(140),
`full_name` varchar(140),
`welcome_email_sent` int(1) not null default 0,
`view_attachments` int(1) not null default 0,
`hide_timesheets` int(1) not null default 0,
`project_status` text,
index `user`(`user`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:32,967 WARNING database DDL Query made to DB:
create table `tabActivity Cost` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`activity_type` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`billing_rate` decimal(21,9) not null default 0,
`costing_rate` decimal(21,9) not null default 0,
`title` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:33,262 WARNING database DDL Query made to DB:
create table `tabActivity Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`activity_type` varchar(140),
`costing_rate` decimal(21,9) not null default 0,
`billing_rate` decimal(21,9) not null default 0,
`disabled` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:33,496 WARNING database DDL Query made to DB:
create table `tabTimesheet` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) default '{employee_name}',
`naming_series` varchar(140),
`company` varchar(140),
`customer` varchar(140),
`currency` varchar(140),
`exchange_rate` decimal(21,9) not null default 1.0,
`sales_invoice` varchar(140),
`status` varchar(140) default 'Draft',
`parent_project` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`user` varchar(140),
`start_date` date,
`end_date` date,
`total_hours` decimal(21,9) not null default 0,
`total_billable_hours` decimal(21,9) not null default 0,
`base_total_billable_amount` decimal(21,9) not null default 0,
`base_total_billed_amount` decimal(21,9) not null default 0,
`base_total_costing_amount` decimal(21,9) not null default 0,
`total_billed_hours` decimal(21,9) not null default 0,
`total_billable_amount` decimal(21,9) not null default 0,
`total_billed_amount` decimal(21,9) not null default 0,
`total_costing_amount` decimal(21,9) not null default 0,
`per_billed` decimal(21,9) not null default 0,
`note` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:33,679 WARNING database DDL Query made to DB:
create table `tabProject Update` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`project` varchar(140),
`sent` int(1) not null default 0,
`date` date,
`time` time(6),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `date`(`date`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:33,809 WARNING database DDL Query made to DB:
create table `tabTask Depends On` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`task` varchar(140),
`subject` text,
`project` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:33,929 WARNING database DDL Query made to DB:
create table `tabProject Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`project_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:34,073 WARNING database DDL Query made to DB:
create table `tabTask Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`weight` decimal(21,9) not null default 0,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:34,206 WARNING database DDL Query made to DB:
create table `tabProject Template Task` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`task` varchar(140),
`subject` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:34,502 WARNING database DDL Query made to DB:
create table `tabProject` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`project_name` varchar(140) unique,
`status` varchar(140) default 'Open',
`project_type` varchar(140),
`is_active` varchar(140),
`percent_complete_method` varchar(140) default 'Task Completion',
`percent_complete` decimal(21,9) not null default 0,
`project_template` varchar(140),
`expected_start_date` date,
`expected_end_date` date,
`priority` varchar(140),
`department` varchar(140),
`customer` varchar(140),
`sales_order` varchar(140),
`copied_from` varchar(140),
`notes` longtext,
`actual_start_date` date,
`actual_time` decimal(21,9) not null default 0,
`actual_end_date` date,
`estimated_costing` decimal(21,9) not null default 0,
`total_costing_amount` decimal(21,9) not null default 0,
`total_purchase_cost` decimal(21,9) not null default 0,
`company` varchar(140),
`total_sales_amount` decimal(21,9) not null default 0,
`total_billable_amount` decimal(21,9) not null default 0,
`total_billed_amount` decimal(21,9) not null default 0,
`total_consumed_material_cost` decimal(21,9) not null default 0,
`cost_center` varchar(140),
`gross_margin` decimal(21,9) not null default 0,
`per_gross_margin` decimal(21,9) not null default 0,
`collect_progress` int(1) not null default 0,
`holiday_list` varchar(140),
`frequency` varchar(140),
`from_time` time(6),
`to_time` time(6),
`first_email` time(6),
`second_email` time(6),
`daily_time_to_send` time(6),
`day_to_send` varchar(140),
`weekly_time_to_send` time(6),
`subject` varchar(140),
`message` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `status`(`status`),
index `customer`(`customer`),
index `collect_progress`(`collect_progress`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:34,668 WARNING database DDL Query made to DB:
create table `tabProject Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`project_type` varchar(140),
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:34,835 WARNING database DDL Query made to DB:
create table `tabDependent Task` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`task` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:35,033 WARNING database DDL Query made to DB:
create table `tabTimesheet Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`activity_type` varchar(140),
`from_time` datetime(6),
`description` text,
`expected_hours` decimal(21,9) not null default 0,
`to_time` datetime(6),
`hours` decimal(21,9) not null default 0,
`completed` int(1) not null default 0,
`project` varchar(140),
`project_name` varchar(140),
`task` varchar(140),
`is_billable` int(1) not null default 0,
`sales_invoice` varchar(140),
`billing_hours` decimal(21,9) not null default 0,
`base_billing_rate` decimal(21,9) not null default 0,
`base_billing_amount` decimal(21,9) not null default 0,
`base_costing_rate` decimal(21,9) not null default 0,
`base_costing_amount` decimal(21,9) not null default 0,
`billing_rate` decimal(21,9) not null default 0,
`billing_amount` decimal(21,9) not null default 0,
`costing_rate` decimal(21,9) not null default 0,
`costing_amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:35,286 WARNING database DDL Query made to DB:
create table `tabTask` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`subject` varchar(140),
`project` varchar(140),
`issue` varchar(140),
`type` varchar(140),
`color` varchar(140),
`is_group` int(1) not null default 0,
`is_template` int(1) not null default 0,
`status` varchar(140),
`priority` varchar(140),
`task_weight` decimal(21,9) not null default 0,
`parent_task` varchar(140),
`completed_by` varchar(140),
`completed_on` date,
`exp_start_date` date,
`expected_time` decimal(21,9) not null default 0,
`start` int(11) not null default 0,
`exp_end_date` date,
`progress` decimal(21,9) not null default 0,
`duration` int(11) not null default 0,
`is_milestone` int(1) not null default 0,
`description` longtext,
`depends_on_tasks` longtext,
`act_start_date` date,
`actual_time` decimal(21,9) not null default 0,
`act_end_date` date,
`total_costing_amount` decimal(21,9) not null default 0,
`total_billing_amount` decimal(21,9) not null default 0,
`review_date` date,
`closing_date` date,
`department` varchar(140),
`company` varchar(140),
`lft` int(11) not null default 0,
`rgt` int(11) not null default 0,
`old_parent` varchar(140),
`template_task` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `subject`(`subject`),
index `project`(`project`),
index `priority`(`priority`),
index `parent_task`(`parent_task`),
index `exp_end_date`(`exp_end_date`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:35,381 WARNING database DDL Query made to DB:
ALTER TABLE `tabTask`
				ADD INDEX IF NOT EXISTS `lft_rgt_index`(lft, rgt)
2025-07-14 09:01:35,987 WARNING database DDL Query made to DB:
create table `tabParty Specific Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`party_type` varchar(140),
`party` varchar(140),
`restrict_based_on` varchar(140),
`based_on_value` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:36,130 WARNING database DDL Query made to DB:
create table `tabSales Partner Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`sales_partner_type` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:36,274 WARNING database DDL Query made to DB:
create table `tabProduct Bundle Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`qty` decimal(21,9) not null default 0,
`description` longtext,
`rate` decimal(21,9) not null default 0,
`uom` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:36,629 WARNING database DDL Query made to DB:
create table `tabQuotation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) default '{customer_name}',
`naming_series` varchar(140),
`quotation_to` varchar(140) default 'Customer',
`party_name` varchar(140),
`customer_name` varchar(140),
`transaction_date` date,
`valid_till` date,
`order_type` varchar(140) default 'Sales',
`company` varchar(140),
`has_unit_price_items` int(1) not null default 0,
`amended_from` varchar(140),
`currency` varchar(140),
`conversion_rate` decimal(21,9) not null default 0,
`selling_price_list` varchar(140),
`price_list_currency` varchar(140),
`plc_conversion_rate` decimal(21,9) not null default 0,
`ignore_pricing_rule` int(1) not null default 0,
`scan_barcode` varchar(140),
`total_qty` decimal(21,9) not null default 0,
`total_net_weight` decimal(21,9) not null default 0,
`base_total` decimal(21,9) not null default 0,
`base_net_total` decimal(21,9) not null default 0,
`total` decimal(21,9) not null default 0,
`net_total` decimal(21,9) not null default 0,
`tax_category` varchar(140),
`taxes_and_charges` varchar(140),
`shipping_rule` varchar(140),
`incoterm` varchar(140),
`named_place` varchar(140),
`base_total_taxes_and_charges` decimal(21,9) not null default 0,
`total_taxes_and_charges` decimal(21,9) not null default 0,
`base_grand_total` decimal(21,9) not null default 0,
`base_rounding_adjustment` decimal(21,9) not null default 0,
`base_rounded_total` decimal(21,9) not null default 0,
`base_in_words` varchar(240),
`grand_total` decimal(21,9) not null default 0,
`rounding_adjustment` decimal(21,9) not null default 0,
`rounded_total` decimal(21,9) not null default 0,
`disable_rounded_total` int(1) not null default 0,
`in_words` varchar(240),
`apply_discount_on` varchar(140) default 'Grand Total',
`base_discount_amount` decimal(21,9) not null default 0,
`coupon_code` varchar(140),
`additional_discount_percentage` decimal(21,9) not null default 0,
`discount_amount` decimal(21,9) not null default 0,
`referral_sales_partner` varchar(140),
`other_charges_calculation` longtext,
`customer_address` varchar(140),
`address_display` text,
`contact_person` varchar(140),
`contact_display` text,
`contact_mobile` text,
`contact_email` varchar(140),
`shipping_address_name` varchar(140),
`shipping_address` text,
`company_address` varchar(140),
`company_address_display` text,
`company_contact_person` varchar(140),
`payment_terms_template` varchar(140),
`tc_name` varchar(140),
`terms` longtext,
`auto_repeat` varchar(140),
`letter_head` varchar(140),
`group_same_items` int(1) not null default 0,
`select_print_heading` varchar(140),
`language` varchar(140),
`order_lost_reason` text,
`status` varchar(140) default 'Draft',
`customer_group` varchar(140),
`territory` varchar(140),
`campaign` varchar(140),
`source` varchar(140),
`opportunity` varchar(140),
`supplier_quotation` varchar(140),
`enq_det` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `party_name`(`party_name`),
index `transaction_date`(`transaction_date`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:37,087 WARNING database DDL Query made to DB:
create table `tabSales Order Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`customer_item_code` varchar(140),
`ensure_delivery_based_on_produced_serial_no` int(1) not null default 0,
`is_stock_item` int(1) not null default 0,
`reserve_stock` int(1) not null default 1,
`delivery_date` date,
`item_name` varchar(140),
`description` longtext,
`item_group` varchar(140),
`brand` varchar(140),
`image` text,
`qty` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`stock_qty` decimal(21,9) not null default 0,
`stock_reserved_qty` decimal(21,9) not null default 0,
`price_list_rate` decimal(21,9) not null default 0,
`base_price_list_rate` decimal(21,9) not null default 0,
`margin_type` varchar(140),
`margin_rate_or_amount` decimal(21,9) not null default 0,
`rate_with_margin` decimal(21,9) not null default 0,
`discount_percentage` decimal(21,9) not null default 0,
`discount_amount` decimal(21,9) not null default 0,
`distributed_discount_amount` decimal(21,9) not null default 0,
`base_rate_with_margin` decimal(21,9) not null default 0,
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`item_tax_template` varchar(140),
`base_rate` decimal(21,9) not null default 0,
`base_amount` decimal(21,9) not null default 0,
`pricing_rules` text,
`stock_uom_rate` decimal(21,9) not null default 0,
`is_free_item` int(1) not null default 0,
`grant_commission` int(1) not null default 0,
`net_rate` decimal(21,9) not null default 0,
`net_amount` decimal(21,9) not null default 0,
`base_net_rate` decimal(21,9) not null default 0,
`base_net_amount` decimal(21,9) not null default 0,
`billed_amt` decimal(21,9) not null default 0,
`valuation_rate` decimal(21,9) not null default 0,
`gross_profit` decimal(21,9) not null default 0,
`delivered_by_supplier` int(1) not null default 0,
`supplier` varchar(140),
`weight_per_unit` decimal(21,9) not null default 0,
`total_weight` decimal(21,9) not null default 0,
`weight_uom` varchar(140),
`warehouse` varchar(140),
`target_warehouse` varchar(140),
`prevdoc_docname` varchar(140),
`quotation_item` varchar(140),
`against_blanket_order` int(1) not null default 0,
`blanket_order` varchar(140),
`blanket_order_rate` decimal(21,9) not null default 0,
`actual_qty` decimal(21,9) not null default 0,
`company_total_stock` decimal(21,9) not null default 0,
`bom_no` varchar(140),
`projected_qty` decimal(21,9) not null default 0,
`ordered_qty` decimal(21,9) not null default 0,
`planned_qty` decimal(21,9) not null default 0,
`production_plan_qty` decimal(21,9) not null default 0,
`work_order_qty` decimal(21,9) not null default 0,
`delivered_qty` decimal(21,9) not null default 0,
`produced_qty` decimal(21,9) not null default 0,
`returned_qty` decimal(21,9) not null default 0,
`picked_qty` decimal(21,9) not null default 0,
`additional_notes` text,
`page_break` int(1) not null default 0,
`item_tax_rate` longtext,
`transaction_date` date,
`material_request` varchar(140),
`purchase_order` varchar(140),
`material_request_item` varchar(140),
`purchase_order_item` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
index `prevdoc_docname`(`prevdoc_docname`),
index `purchase_order`(`purchase_order`),
index `project`(`project`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:37,167 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order Item`
				ADD INDEX IF NOT EXISTS `item_code_warehouse_index`(item_code, warehouse)
2025-07-14 09:01:37,334 WARNING database DDL Query made to DB:
create table `tabInstallation Note Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`serial_and_batch_bundle` varchar(140),
`serial_no` text,
`qty` decimal(21,9) not null default 0,
`description` longtext,
`prevdoc_detail_docname` varchar(140),
`prevdoc_docname` varchar(140),
`prevdoc_doctype` varchar(140),
index `prevdoc_docname`(`prevdoc_docname`),
index `prevdoc_doctype`(`prevdoc_doctype`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:37,498 WARNING database DDL Query made to DB:
create table `tabIndustry Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`industry` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:38,082 WARNING database DDL Query made to DB:
create table `tabSales Order` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) default '{customer_name}',
`naming_series` varchar(140),
`customer` varchar(140),
`customer_name` varchar(140),
`tax_id` varchar(140),
`order_type` varchar(140) default 'Sales',
`transaction_date` date,
`delivery_date` date,
`po_no` varchar(140),
`po_date` date,
`company` varchar(140),
`skip_delivery_note` int(1) not null default 0,
`has_unit_price_items` int(1) not null default 0,
`amended_from` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`currency` varchar(140),
`conversion_rate` decimal(21,9) not null default 0,
`selling_price_list` varchar(140),
`price_list_currency` varchar(140),
`plc_conversion_rate` decimal(21,9) not null default 0,
`ignore_pricing_rule` int(1) not null default 0,
`scan_barcode` varchar(140),
`set_warehouse` varchar(140),
`reserve_stock` int(1) not null default 0,
`total_qty` decimal(21,9) not null default 0,
`total_net_weight` decimal(21,9) not null default 0,
`base_total` decimal(21,9) not null default 0,
`base_net_total` decimal(21,9) not null default 0,
`total` decimal(21,9) not null default 0,
`net_total` decimal(21,9) not null default 0,
`tax_category` varchar(140),
`taxes_and_charges` varchar(140),
`shipping_rule` varchar(140),
`incoterm` varchar(140),
`named_place` varchar(140),
`base_total_taxes_and_charges` decimal(21,9) not null default 0,
`total_taxes_and_charges` decimal(21,9) not null default 0,
`base_grand_total` decimal(21,9) not null default 0,
`base_rounding_adjustment` decimal(21,9) not null default 0,
`base_rounded_total` decimal(21,9) not null default 0,
`base_in_words` varchar(240),
`grand_total` decimal(21,9) not null default 0,
`rounding_adjustment` decimal(21,9) not null default 0,
`rounded_total` decimal(21,9) not null default 0,
`in_words` varchar(240),
`advance_paid` decimal(21,9) not null default 0,
`disable_rounded_total` int(1) not null default 0,
`apply_discount_on` varchar(140) default 'Grand Total',
`base_discount_amount` decimal(21,9) not null default 0,
`coupon_code` varchar(140),
`additional_discount_percentage` decimal(21,9) not null default 0,
`discount_amount` decimal(21,9) not null default 0,
`other_charges_calculation` longtext,
`customer_address` varchar(140),
`address_display` text,
`customer_group` varchar(140),
`territory` varchar(140),
`contact_person` varchar(140),
`contact_display` text,
`contact_phone` varchar(140),
`contact_mobile` text,
`contact_email` varchar(140),
`shipping_address_name` varchar(140),
`shipping_address` text,
`dispatch_address_name` varchar(140),
`dispatch_address` text,
`company_address` varchar(140),
`company_address_display` text,
`company_contact_person` varchar(140),
`payment_terms_template` varchar(140),
`tc_name` varchar(140),
`terms` longtext,
`status` varchar(140) default 'Draft',
`delivery_status` varchar(140),
`per_delivered` decimal(21,9) not null default 0,
`per_billed` decimal(21,9) not null default 0,
`per_picked` decimal(21,9) not null default 0,
`billing_status` varchar(140),
`sales_partner` varchar(140),
`amount_eligible_for_commission` decimal(21,9) not null default 0,
`commission_rate` decimal(21,9) not null default 0,
`total_commission` decimal(21,9) not null default 0,
`loyalty_points` int(11) not null default 0,
`loyalty_amount` decimal(21,9) not null default 0,
`from_date` date,
`to_date` date,
`auto_repeat` varchar(140),
`letter_head` varchar(140),
`group_same_items` int(1) not null default 0,
`select_print_heading` varchar(140),
`language` varchar(140),
`is_internal_customer` int(1) not null default 0,
`represents_company` varchar(140),
`source` varchar(140),
`inter_company_order_reference` varchar(140),
`campaign` varchar(140),
`party_account_currency` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `customer`(`customer`),
index `transaction_date`(`transaction_date`),
index `project`(`project`),
index `status`(`status`),
index `inter_company_order_reference`(`inter_company_order_reference`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:38,361 WARNING database DDL Query made to DB:
create table `tabProduct Bundle` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`new_item_code` varchar(140),
`description` varchar(140),
`disabled` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:38,750 WARNING database DDL Query made to DB:
create table `tabCustomer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`salutation` varchar(140),
`customer_name` varchar(140),
`customer_type` varchar(140) default 'Company',
`customer_group` varchar(140),
`territory` varchar(140),
`gender` varchar(140),
`lead_name` varchar(140),
`opportunity_name` varchar(140),
`prospect_name` varchar(140),
`account_manager` varchar(140),
`image` text,
`default_currency` varchar(140),
`default_bank_account` varchar(140),
`default_price_list` varchar(140),
`is_internal_customer` int(1) not null default 0,
`represents_company` varchar(140) unique,
`market_segment` varchar(140),
`industry` varchar(140),
`customer_pos_id` varchar(140),
`website` varchar(140),
`language` varchar(140),
`customer_details` text,
`customer_primary_address` varchar(140),
`primary_address` text,
`customer_primary_contact` varchar(140),
`mobile_no` varchar(140),
`email_id` varchar(140),
`tax_id` varchar(140),
`tax_category` varchar(140),
`tax_withholding_category` varchar(140),
`payment_terms` varchar(140),
`loyalty_program` varchar(140),
`loyalty_program_tier` varchar(140),
`default_sales_partner` varchar(140),
`default_commission_rate` decimal(21,9) not null default 0,
`so_required` int(1) not null default 0,
`dn_required` int(1) not null default 0,
`is_frozen` int(1) not null default 0,
`disabled` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `customer_name`(`customer_name`),
index `customer_group`(`customer_group`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:38,950 WARNING database DDL Query made to DB:
create table `tabSales Team` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`sales_person` varchar(140),
`contact_no` varchar(140),
`allocated_percentage` decimal(21,9) not null default 0,
`allocated_amount` decimal(21,9) not null default 0,
`commission_rate` varchar(140),
`incentives` decimal(21,9) not null default 0,
index `sales_person`(`sales_person`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:39,225 WARNING database DDL Query made to DB:
create table `tabCustomer Credit Limit` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`credit_limit` decimal(21,9) not null default 0,
`bypass_credit_limit_check` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:39,422 WARNING database DDL Query made to DB:
create table `tabInstallation Note` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`customer` varchar(140),
`customer_address` varchar(140),
`contact_person` varchar(140),
`customer_name` varchar(140),
`address_display` text,
`contact_display` text,
`contact_mobile` text,
`contact_email` varchar(140),
`territory` varchar(140),
`customer_group` varchar(140),
`inst_date` date,
`inst_time` time(6),
`status` varchar(140) default 'Draft',
`company` varchar(140),
`amended_from` varchar(140),
`remarks` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `customer`(`customer`),
index `territory`(`territory`),
index `inst_date`(`inst_date`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:39,711 WARNING database DDL Query made to DB:
create table `tabQuotation Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`customer_item_code` varchar(140),
`item_name` varchar(140),
`description` longtext,
`item_group` varchar(140),
`brand` varchar(140),
`image` text,
`qty` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`stock_qty` decimal(21,9) not null default 0,
`actual_qty` decimal(21,9) not null default 0,
`company_total_stock` decimal(21,9) not null default 0,
`price_list_rate` decimal(21,9) not null default 0,
`base_price_list_rate` decimal(21,9) not null default 0,
`margin_type` varchar(140),
`margin_rate_or_amount` decimal(21,9) not null default 0,
`rate_with_margin` decimal(21,9) not null default 0,
`discount_percentage` decimal(21,9) not null default 0,
`discount_amount` decimal(21,9) not null default 0,
`distributed_discount_amount` decimal(21,9) not null default 0,
`base_rate_with_margin` decimal(21,9) not null default 0,
`rate` decimal(21,9) not null default 0,
`net_rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`net_amount` decimal(21,9) not null default 0,
`item_tax_template` varchar(140),
`base_rate` decimal(21,9) not null default 0,
`base_net_rate` decimal(21,9) not null default 0,
`base_amount` decimal(21,9) not null default 0,
`base_net_amount` decimal(21,9) not null default 0,
`pricing_rules` text,
`stock_uom_rate` decimal(21,9) not null default 0,
`is_free_item` int(1) not null default 0,
`is_alternative` int(1) not null default 0,
`has_alternative_item` int(1) not null default 0,
`valuation_rate` decimal(21,9) not null default 0,
`gross_profit` decimal(21,9) not null default 0,
`weight_per_unit` decimal(21,9) not null default 0,
`total_weight` decimal(21,9) not null default 0,
`weight_uom` varchar(140),
`warehouse` varchar(140),
`against_blanket_order` int(1) not null default 0,
`blanket_order` varchar(140),
`blanket_order_rate` decimal(21,9) not null default 0,
`prevdoc_doctype` varchar(140),
`prevdoc_docname` varchar(140),
`projected_qty` decimal(21,9) not null default 0,
`item_tax_rate` longtext,
`additional_notes` text,
`page_break` int(1) not null default 0,
index `item_code`(`item_code`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:40,967 WARNING database DDL Query made to DB:
create table `tabWebsite Item Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_group` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:41,111 WARNING database DDL Query made to DB:
create table `tabUOM Conversion Factor` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`category` varchar(140),
`from_uom` varchar(140),
`to_uom` varchar(140),
`value` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:41,275 WARNING database DDL Query made to DB:
create table `tabHoliday List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`holiday_list_name` varchar(140) unique,
`from_date` date,
`to_date` date,
`total_holidays` int(11) not null default 0,
`weekly_off` varchar(140),
`country` varchar(140),
`subdivision` varchar(140),
`color` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:41,486 WARNING database DDL Query made to DB:
create table `tabEmail Digest` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enabled` int(1) not null default 0,
`company` varchar(140),
`frequency` varchar(140),
`next_send` varchar(140),
`income` int(1) not null default 0,
`expenses_booked` int(1) not null default 0,
`income_year_to_date` int(1) not null default 0,
`expense_year_to_date` int(1) not null default 0,
`bank_balance` int(1) not null default 0,
`credit_balance` int(1) not null default 0,
`invoiced_amount` int(1) not null default 0,
`payables` int(1) not null default 0,
`sales_orders_to_bill` int(1) not null default 0,
`purchase_orders_to_bill` int(1) not null default 0,
`sales_order` int(1) not null default 0,
`purchase_order` int(1) not null default 0,
`sales_orders_to_deliver` int(1) not null default 0,
`purchase_orders_to_receive` int(1) not null default 0,
`sales_invoice` int(1) not null default 0,
`purchase_invoice` int(1) not null default 0,
`new_quotations` int(1) not null default 0,
`pending_quotations` int(1) not null default 0,
`issue` int(1) not null default 0,
`project` int(1) not null default 0,
`purchase_orders_items_overdue` int(1) not null default 0,
`calendar_events` int(1) not null default 0,
`todo_list` int(1) not null default 0,
`notifications` int(1) not null default 0,
`add_quote` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:41,635 WARNING database DDL Query made to DB:
create table `tabUOM` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enabled` int(1) not null default 1,
`uom_name` varchar(140) unique,
`must_be_whole_number` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:41,932 WARNING database DDL Query made to DB:
create table `tabTarget Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_group` varchar(140),
`fiscal_year` varchar(140),
`target_qty` decimal(21,9) not null default 0,
`target_amount` decimal(21,9) not null default 0,
`distribution_id` varchar(140),
index `item_group`(`item_group`),
index `fiscal_year`(`fiscal_year`),
index `target_amount`(`target_amount`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:42,039 WARNING database DDL Query made to DB:
create table `tabQuotation Lost Reason Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lost_reason` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:42,199 WARNING database DDL Query made to DB:
create table `tabEmployee External Work History` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company_name` varchar(140),
`designation` varchar(140),
`salary` decimal(21,9) not null default 0,
`address` text,
`contact` varchar(140),
`total_experience` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:42,377 WARNING database DDL Query made to DB:
create table `tabItem Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_group_name` varchar(140) unique,
`parent_item_group` varchar(140),
`is_group` int(1) not null default 0,
`image` text,
`lft` int(11) not null default 0,
`old_parent` varchar(140),
`rgt` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `lft`(`lft`),
index `rgt`(`rgt`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:42,603 WARNING database DDL Query made to DB:
create table `tabTerritory` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`territory_name` varchar(140) unique,
`parent_territory` varchar(140),
`is_group` int(1) not null default 0,
`territory_manager` varchar(140),
`lft` int(11) not null default 0,
`rgt` int(11) not null default 0,
`old_parent` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `territory_manager`(`territory_manager`),
index `lft`(`lft`),
index `rgt`(`rgt`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:42,738 WARNING database DDL Query made to DB:
ALTER TABLE `tabTerritory`
				ADD INDEX IF NOT EXISTS `lft_rgt_index`(lft, rgt)
2025-07-14 09:01:42,832 WARNING database DDL Query made to DB:
create table `tabQuotation Lost Reason` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`order_lost_reason` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:43,049 WARNING database DDL Query made to DB:
create table `tabAuthorization Rule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`transaction` varchar(140),
`based_on` varchar(140),
`customer_or_item` varchar(140),
`master_name` varchar(140),
`company` varchar(140),
`value` decimal(21,9) not null default 0,
`system_role` varchar(140),
`to_emp` varchar(140),
`system_user` varchar(140),
`to_designation` varchar(140),
`approving_role` varchar(140),
`approving_user` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:43,173 WARNING database DDL Query made to DB:
create table `tabEmployee Internal Work History` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`branch` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`from_date` date,
`to_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:43,297 WARNING database DDL Query made to DB:
create table `tabEmail Digest Recipient` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`recipient` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:43,437 WARNING database DDL Query made to DB:
create table `tabBranch` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`branch` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:43,571 WARNING database DDL Query made to DB:
create table `tabDriving License Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`class` varchar(140),
`description` varchar(140),
`issuing_date` date,
`expiry_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:43,902 WARNING database DDL Query made to DB:
create table `tabEmployee` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`naming_series` varchar(140),
`first_name` varchar(140),
`middle_name` varchar(140),
`last_name` varchar(140),
`employee_name` varchar(140),
`gender` varchar(140),
`date_of_birth` date,
`salutation` varchar(140),
`date_of_joining` date,
`image` text,
`status` varchar(140) default 'Active',
`user_id` varchar(140),
`create_user_permission` int(1) not null default 1,
`company` varchar(140),
`department` varchar(140),
`employee_number` varchar(140),
`designation` varchar(140),
`reports_to` varchar(140),
`branch` varchar(140),
`scheduled_confirmation_date` date,
`final_confirmation_date` date,
`contract_end_date` date,
`notice_number_of_days` int(11) not null default 0,
`date_of_retirement` date,
`cell_number` varchar(140),
`personal_email` varchar(140),
`company_email` varchar(140),
`prefered_contact_email` varchar(140),
`prefered_email` varchar(140),
`unsubscribed` int(1) not null default 0,
`current_address` text,
`current_accommodation_type` varchar(140),
`permanent_address` text,
`permanent_accommodation_type` varchar(140),
`person_to_be_contacted` varchar(140),
`emergency_phone_number` varchar(140),
`relation` varchar(140),
`attendance_device_id` varchar(140) unique,
`holiday_list` varchar(140),
`ctc` decimal(21,9) not null default 0,
`salary_currency` varchar(140),
`salary_mode` varchar(140),
`bank_name` varchar(140),
`bank_ac_no` varchar(140),
`iban` varchar(140),
`marital_status` varchar(140),
`family_background` text,
`blood_group` varchar(140),
`health_details` text,
`passport_number` varchar(140),
`valid_upto` date,
`date_of_issue` date,
`place_of_issue` varchar(140),
`bio` longtext,
`resignation_letter_date` date,
`relieving_date` date,
`held_on` date,
`new_workplace` varchar(140),
`leave_encashed` varchar(140),
`encashment_date` date,
`reason_for_leaving` text,
`feedback` text,
`lft` int(11) not null default 0,
`rgt` int(11) not null default 0,
`old_parent` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `status`(`status`),
index `designation`(`designation`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 09:01:43,988 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee`
				ADD INDEX IF NOT EXISTS `lft_rgt_index`(lft, rgt)
2025-07-14 09:01:44,058 WARNING database DDL Query made to DB:
create table `tabEmployee Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee_group_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
