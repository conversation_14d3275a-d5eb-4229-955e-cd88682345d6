2025-07-14 16:19:57,969 WARNING database DDL Query made to DB:
create table `tabService Level Priority` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`default_priority` int(1) not null default 0,
`priority` varchar(140),
`response_time` decimal(21,9),
`resolution_time` decimal(21,9),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:58,211 WARNING database DDL Query made to DB:
create table `tabWarranty Claim` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`status` varchar(140) default 'Open',
`complaint_date` date,
`customer` varchar(140),
`serial_no` varchar(140),
`complaint` longtext,
`item_code` varchar(140),
`item_name` varchar(140),
`description` text,
`warranty_amc_status` varchar(140),
`warranty_expiry_date` date,
`amc_expiry_date` date,
`resolution_date` datetime(6),
`resolved_by` varchar(140),
`resolution_details` text,
`customer_name` varchar(140),
`contact_person` varchar(140),
`contact_display` text,
`contact_mobile` varchar(140),
`contact_email` varchar(140),
`territory` varchar(140),
`customer_group` varchar(140),
`customer_address` varchar(140),
`address_display` text,
`service_address` text,
`company` varchar(140),
`complaint_raised_by` varchar(140),
`from_company` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `status`(`status`),
index `complaint_date`(`complaint_date`),
index `customer`(`customer`),
index `serial_no`(`serial_no`),
index `item_code`(`item_code`),
index `warranty_amc_status`(`warranty_amc_status`),
index `resolution_date`(`resolution_date`),
index `resolved_by`(`resolved_by`),
index `territory`(`territory`),
index `company`(`company`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:58,339 WARNING database DDL Query made to DB:
create table `tabService Day` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`workday` varchar(140),
`start_time` time(6),
`end_time` time(6),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:58,450 WARNING database DDL Query made to DB:
create table `tabSLA Fulfilled On Status` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:58,633 WARNING database DDL Query made to DB:
create table `tabService Level Agreement` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`default_priority` varchar(140),
`service_level` varchar(140),
`enabled` int(1) not null default 1,
`default_service_level_agreement` int(1) not null default 0,
`entity_type` varchar(140),
`entity` varchar(140),
`condition` longtext,
`start_date` date,
`end_date` date,
`apply_sla_for_resolution` int(1) not null default 1,
`holiday_list` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:58,759 WARNING database DDL Query made to DB:
create table `tabIssue Priority` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:59,020 WARNING database DDL Query made to DB:
create table `tabPortal User` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
index `user`(`user`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:59,361 WARNING database DDL Query made to DB:
create table `tabSMS Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`sender_name` varchar(140),
`sent_on` date,
`message` text,
`no_of_requested_sms` int(11) not null default 0,
`requested_numbers` longtext,
`no_of_sent_sms` int(11) not null default 0,
`sent_to` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:59,541 WARNING database DDL Query made to DB:
create table `tabVideo` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) unique,
`provider` varchar(140),
`url` varchar(140),
`youtube_video_id` varchar(140),
`publish_date` date,
`duration` decimal(21,9),
`like_count` decimal(21,9) not null default 0,
`view_count` decimal(21,9) not null default 0,
`dislike_count` decimal(21,9) not null default 0,
`comment_count` decimal(21,9) not null default 0,
`description` longtext,
`image` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:19:59,988 WARNING database DDL Query made to DB:
create table `tabAsset Category Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company_name` varchar(140),
`fixed_asset_account` varchar(140),
`accumulated_depreciation_account` varchar(140),
`depreciation_expense_account` varchar(140),
`capital_work_in_progress_account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:00,140 WARNING database DDL Query made to DB:
create table `tabAsset Maintenance Team` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`maintenance_team_name` varchar(140) unique,
`maintenance_manager` varchar(140),
`maintenance_manager_name` varchar(140),
`company` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:00,284 WARNING database DDL Query made to DB:
create table `tabAsset Movement Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`asset` varchar(140),
`source_location` varchar(140),
`from_employee` varchar(140),
`asset_name` varchar(140),
`target_location` varchar(140),
`to_employee` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:00,551 WARNING database DDL Query made to DB:
create table `tabAsset` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`asset_owner` varchar(140),
`asset_owner_company` varchar(140),
`is_existing_asset` int(1) not null default 0,
`is_composite_asset` int(1) not null default 0,
`supplier` varchar(140),
`customer` varchar(140),
`image` text,
`journal_entry_for_scrap` varchar(140),
`naming_series` varchar(140),
`asset_name` varchar(140),
`asset_category` varchar(140),
`location` varchar(140),
`split_from` varchar(140),
`custodian` varchar(140),
`department` varchar(140),
`disposal_date` date,
`cost_center` varchar(140),
`purchase_receipt` varchar(140),
`purchase_receipt_item` varchar(140),
`purchase_invoice` varchar(140),
`purchase_invoice_item` varchar(140),
`purchase_date` date,
`available_for_use_date` date,
`gross_purchase_amount` decimal(21,9) not null default 0,
`asset_quantity` int(11) not null default 1,
`additional_asset_cost` decimal(21,9) not null default 0,
`total_asset_cost` decimal(21,9) not null default 0,
`calculate_depreciation` int(1) not null default 0,
`opening_accumulated_depreciation` decimal(21,9) not null default 0,
`opening_number_of_booked_depreciations` int(11) not null default 0,
`is_fully_depreciated` int(1) not null default 0,
`depreciation_method` varchar(140),
`value_after_depreciation` decimal(21,9) not null default 0,
`total_number_of_depreciations` int(11) not null default 0,
`frequency_of_depreciation` int(11) not null default 0,
`next_depreciation_date` date,
`policy_number` varchar(140),
`insurer` varchar(140),
`insured_value` varchar(140),
`insurance_start_date` date,
`insurance_end_date` date,
`comprehensive_insurance` varchar(140),
`maintenance_required` int(1) not null default 0,
`status` varchar(140) default 'Draft',
`booked_fixed_asset` int(1) not null default 0,
`purchase_amount` decimal(21,9) not null default 0,
`default_finance_book` varchar(140),
`depr_entry_posting_status` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:00,689 WARNING database DDL Query made to DB:
create table `tabAsset Shift Factor` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`shift_name` varchar(140) unique,
`shift_factor` decimal(21,9) not null default 0,
`default` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:00,870 WARNING database DDL Query made to DB:
create table `tabAsset Capitalization Stock Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`warehouse` varchar(140),
`purchase_receipt_item` varchar(140),
`stock_qty` decimal(21,9) not null default 0,
`actual_qty` decimal(21,9) not null default 0,
`valuation_rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`serial_and_batch_bundle` varchar(140),
`use_serial_batch_fields` int(1) not null default 0,
`serial_no` text,
`batch_no` varchar(140),
`cost_center` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:01,022 WARNING database DDL Query made to DB:
create table `tabAsset Activity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`asset` varchar(140),
`date` datetime(6),
`user` varchar(140),
`subject` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:01,224 WARNING database DDL Query made to DB:
create table `tabAsset Maintenance Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`asset_maintenance` varchar(140),
`naming_series` varchar(140),
`asset_name` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`task` varchar(140),
`task_name` varchar(140),
`maintenance_type` varchar(140),
`periodicity` varchar(140),
`has_certificate` int(1) not null default 0,
`certificate_attachement` text,
`maintenance_status` varchar(140),
`assign_to_name` varchar(140),
`task_assignee_email` varchar(140),
`due_date` date,
`completion_date` date,
`description` varchar(140),
`actions_performed` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:01,377 WARNING database DDL Query made to DB:
create table `tabDepreciation Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`schedule_date` date,
`depreciation_amount` decimal(21,9) not null default 0,
`accumulated_depreciation_amount` decimal(21,9) not null default 0,
`journal_entry` varchar(140),
`shift` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:01,527 WARNING database DDL Query made to DB:
create table `tabAsset Capitalization Service Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`expense_account` varchar(140),
`qty` decimal(21,9) not null default 1.0,
`uom` varchar(140),
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`cost_center` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:01,693 WARNING database DDL Query made to DB:
create table `tabAsset Maintenance Task` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`maintenance_task` varchar(140),
`maintenance_type` varchar(140),
`maintenance_status` varchar(140),
`start_date` date,
`periodicity` varchar(140),
`end_date` date,
`certificate_required` int(1) not null default 0,
`assign_to` varchar(140),
`assign_to_name` varchar(140),
`next_due_date` date,
`last_completion_date` date,
`description` longtext,
index `certificate_required`(`certificate_required`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:01,913 WARNING database DDL Query made to DB:
create table `tabAsset Capitalization` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`naming_series` varchar(140),
`capitalization_method` varchar(140),
`target_item_code` varchar(140),
`target_item_name` varchar(140),
`target_asset` varchar(140),
`target_asset_name` varchar(140),
`target_qty` decimal(21,9) not null default 1.0,
`target_asset_location` varchar(140),
`company` varchar(140),
`posting_date` date,
`posting_time` time(6),
`set_posting_time` int(1) not null default 0,
`finance_book` varchar(140),
`target_batch_no` varchar(140),
`target_serial_no` text,
`amended_from` varchar(140),
`target_is_fixed_asset` int(1) not null default 0,
`target_has_batch_no` int(1) not null default 0,
`target_has_serial_no` int(1) not null default 0,
`stock_items_total` decimal(21,9) not null default 0,
`asset_items_total` decimal(21,9) not null default 0,
`service_items_total` decimal(21,9) not null default 0,
`total_value` decimal(21,9) not null default 0,
`target_incoming_rate` decimal(21,9) not null default 0,
`cost_center` varchar(140),
`target_fixed_asset_account` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `posting_date`(`posting_date`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:02,103 WARNING database DDL Query made to DB:
create table `tabAsset Depreciation Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`asset` varchar(140),
`naming_series` varchar(140),
`company` varchar(140),
`gross_purchase_amount` decimal(21,9) not null default 0,
`opening_accumulated_depreciation` decimal(21,9) not null default 0,
`opening_number_of_booked_depreciations` int(11) not null default 0,
`finance_book` varchar(140),
`finance_book_id` int(11) not null default 0,
`depreciation_method` varchar(140),
`total_number_of_depreciations` int(11) not null default 0,
`rate_of_depreciation` decimal(21,9) not null default 0,
`daily_prorata_based` int(1) not null default 0,
`shift_based` int(1) not null default 0,
`frequency_of_depreciation` int(11) not null default 0,
`expected_value_after_useful_life` decimal(21,9) not null default 0,
`notes` text,
`status` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:02,286 WARNING database DDL Query made to DB:
create table `tabAsset Value Adjustment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`asset` varchar(140),
`asset_category` varchar(140),
`date` date,
`finance_book` varchar(140),
`amended_from` varchar(140),
`current_asset_value` decimal(21,9) not null default 0,
`new_asset_value` decimal(21,9) not null default 0,
`difference_amount` decimal(21,9) not null default 0,
`difference_account` varchar(140),
`journal_entry` varchar(140),
`cost_center` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:02,470 WARNING database DDL Query made to DB:
create table `tabAsset Repair Consumed Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`warehouse` varchar(140),
`valuation_rate` decimal(21,9) not null default 0,
`consumed_quantity` varchar(140),
`total_value` decimal(21,9) not null default 0,
`serial_no` text,
`serial_and_batch_bundle` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:02,615 WARNING database DDL Query made to DB:
create table `tabAsset Finance Book` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`finance_book` varchar(140),
`depreciation_method` varchar(140),
`total_number_of_depreciations` int(11) not null default 0,
`total_number_of_booked_depreciations` int(11) not null default 0,
`daily_prorata_based` int(1) not null default 0,
`shift_based` int(1) not null default 0,
`frequency_of_depreciation` int(11) not null default 0,
`depreciation_start_date` date,
`salvage_value_percentage` decimal(21,9) not null default 0,
`expected_value_after_useful_life` decimal(21,9) not null default 0,
`value_after_depreciation` decimal(21,9) not null default 0,
`rate_of_depreciation` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:02,807 WARNING database DDL Query made to DB:
create table `tabAsset Repair` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`asset` varchar(140),
`company` varchar(140),
`asset_name` varchar(140),
`naming_series` varchar(140),
`failure_date` datetime(6),
`repair_status` varchar(140) default 'Pending',
`completion_date` datetime(6),
`cost_center` varchar(140),
`project` varchar(140),
`purchase_invoice` varchar(140),
`capitalize_repair_cost` int(1) not null default 0,
`stock_consumption` int(1) not null default 0,
`repair_cost` decimal(21,9) not null default 0,
`total_repair_cost` decimal(21,9) not null default 0,
`increase_in_asset_life` int(11) not null default 0,
`description` longtext,
`actions_performed` longtext,
`downtime` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:02,979 WARNING database DDL Query made to DB:
create table `tabAsset Capitalization Asset Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`asset` varchar(140),
`asset_name` varchar(140),
`finance_book` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`current_asset_value` decimal(21,9) not null default 0,
`asset_value` decimal(21,9) not null default 0,
`cost_center` varchar(140),
`fixed_asset_account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:03,132 WARNING database DDL Query made to DB:
create table `tabAsset Movement` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`purpose` varchar(140),
`transaction_date` datetime(6),
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:03,320 WARNING database DDL Query made to DB:
create table `tabAsset Maintenance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`asset_name` varchar(140) unique,
`asset_category` varchar(140),
`company` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`maintenance_team` varchar(140),
`maintenance_manager` varchar(140),
`maintenance_manager_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:03,458 WARNING database DDL Query made to DB:
create table `tabMaintenance Team Member` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`team_member` varchar(140),
`full_name` varchar(140),
`maintenance_role` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:03,611 WARNING database DDL Query made to DB:
create table `tabAsset Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`asset_category_name` varchar(140) unique,
`enable_cwip_accounting` int(1) not null default 0,
`non_depreciable_category` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:03,787 WARNING database DDL Query made to DB:
create table `tabAsset Shift Allocation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`asset` varchar(140),
`naming_series` varchar(140),
`finance_book` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:03,923 WARNING database DDL Query made to DB:
create table `tabLinked Location` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`location` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:04,092 WARNING database DDL Query made to DB:
create table `tabLocation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`location_name` varchar(140) unique,
`parent_location` varchar(140),
`is_container` int(1) not null default 0,
`is_group` int(1) not null default 0,
`latitude` decimal(21,9) not null default 0,
`longitude` decimal(21,9) not null default 0,
`area` decimal(21,9) not null default 0,
`area_uom` varchar(140),
`location` longtext,
`lft` int(11) not null default 0,
`rgt` int(11) not null default 0,
`old_parent` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `parent_location`(`parent_location`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:04,242 WARNING database DDL Query made to DB:
ALTER TABLE `tabLocation`
				ADD INDEX IF NOT EXISTS `lft_rgt_index`(lft, rgt)
2025-07-14 16:20:04,727 WARNING database DDL Query made to DB:
create table `tabHomepage Section` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`section_based_on` varchar(140),
`no_of_columns` varchar(140) default '3',
`section_html` longtext,
`section_order` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:04,879 WARNING database DDL Query made to DB:
create table `tabHomepage Section Card` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`subtitle` varchar(140),
`image` text,
`content` text,
`route` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:04,997 WARNING database DDL Query made to DB:
create table `tabWebsite Attribute` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`attribute` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:05,117 WARNING database DDL Query made to DB:
create table `tabWebsite Filter Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`fieldname` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:05,292 WARNING database DDL Query made to DB:
create table `tabMaintenance Schedule Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`scheduled_date` date,
`actual_date` date,
`sales_person` varchar(140),
`completion_status` varchar(140) default 'Pending',
`serial_no` text,
`item_reference` varchar(140),
index `item_code`(`item_code`),
index `scheduled_date`(`scheduled_date`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:05,479 WARNING database DDL Query made to DB:
create table `tabMaintenance Visit` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`customer` varchar(140),
`customer_name` varchar(140),
`address_display` text,
`contact_display` text,
`contact_mobile` varchar(140),
`contact_email` varchar(140),
`maintenance_schedule` varchar(140),
`maintenance_schedule_detail` varchar(140),
`mntc_date` date,
`mntc_time` time(6),
`completion_status` varchar(140),
`maintenance_type` varchar(140) default 'Unscheduled',
`customer_feedback` text,
`status` varchar(140) default 'Draft',
`amended_from` varchar(140),
`company` varchar(140),
`customer_address` varchar(140),
`contact_person` varchar(140),
`territory` varchar(140),
`customer_group` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:05,632 WARNING database DDL Query made to DB:
create table `tabMaintenance Visit Purpose` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`service_person` varchar(140),
`serial_no` varchar(140),
`description` longtext,
`work_done` text,
`prevdoc_doctype` varchar(140),
`prevdoc_docname` varchar(140),
`maintenance_schedule_detail` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:05,803 WARNING database DDL Query made to DB:
create table `tabMaintenance Schedule Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`description` longtext,
`start_date` date,
`end_date` date,
`periodicity` varchar(140),
`no_of_visits` int(11) not null default 0,
`sales_person` varchar(140),
`serial_no` text,
`sales_order` varchar(140),
`serial_and_batch_bundle` varchar(140),
index `item_code`(`item_code`),
index `start_date`(`start_date`),
index `end_date`(`end_date`),
index `sales_order`(`sales_order`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:05,975 WARNING database DDL Query made to DB:
create table `tabMaintenance Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`customer` varchar(140),
`status` varchar(140) default 'Draft',
`transaction_date` date,
`customer_name` varchar(140),
`contact_person` varchar(140),
`contact_mobile` varchar(140),
`contact_email` varchar(140),
`contact_display` text,
`customer_address` varchar(140),
`address_display` text,
`territory` varchar(140),
`customer_group` varchar(140),
`company` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `customer`(`customer`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:06,197 WARNING database DDL Query made to DB:
create table `tabLower Deduction Certificate` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`tax_withholding_category` varchar(140),
`fiscal_year` varchar(140),
`company` varchar(140),
`certificate_no` varchar(140) unique,
`supplier` varchar(140),
`pan_no` varchar(140),
`valid_from` date,
`valid_upto` date,
`rate` decimal(21,9) not null default 0,
`certificate_limit` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:06,343 WARNING database DDL Query made to DB:
create table `tabUAE VAT Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:06,478 WARNING database DDL Query made to DB:
create table `tabUAE VAT Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:06,612 WARNING database DDL Query made to DB:
create table `tabSouth Africa VAT Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:06,789 WARNING database DDL Query made to DB:
create table `tabImport Supplier Invoice` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`invoice_series` varchar(140),
`company` varchar(140),
`item_code` varchar(140),
`supplier_group` varchar(140),
`tax_account` varchar(140),
`default_buying_price_list` varchar(140),
`zip_file` text,
`status` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:07,324 WARNING database DDL Query made to DB:
create table `tabQuality Action Resolution` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`problem` longtext,
`resolution` longtext,
`status` varchar(140),
`responsible` varchar(140),
`completion_by` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:07,448 WARNING database DDL Query made to DB:
create table `tabQuality Feedback Parameter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`parameter` varchar(140),
`rating` varchar(140) default '1',
`feedback` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:07,592 WARNING database DDL Query made to DB:
create table `tabQuality Action` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`corrective_preventive` varchar(140) default 'Corrective',
`review` varchar(140),
`feedback` varchar(140),
`status` varchar(140) default 'Open',
`date` date,
`goal` varchar(140),
`procedure` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:07,733 WARNING database DDL Query made to DB:
create table `tabQuality Meeting Minutes` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`document_name` varchar(140),
`minute` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:07,879 WARNING database DDL Query made to DB:
create table `tabQuality Feedback Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:08,004 WARNING database DDL Query made to DB:
create table `tabQuality Feedback Template Parameter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`parameter` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:08,164 WARNING database DDL Query made to DB:
create table `tabQuality Procedure` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`quality_procedure_name` varchar(140) unique,
`process_owner` varchar(140),
`process_owner_full_name` varchar(140),
`parent_quality_procedure` varchar(140),
`is_group` int(1) not null default 0,
`rgt` int(11) not null default 0,
`lft` int(11) not null default 0,
`old_parent` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:08,302 WARNING database DDL Query made to DB:
create table `tabQuality Goal Objective` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`objective` text,
`target` varchar(140),
`uom` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:08,457 WARNING database DDL Query made to DB:
create table `tabNon Conformance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`subject` varchar(140),
`procedure` varchar(140),
`process_owner` varchar(140),
`full_name` varchar(140),
`status` varchar(140),
`details` longtext,
`corrective_action` longtext,
`preventive_action` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:08,613 WARNING database DDL Query made to DB:
create table `tabQuality Review Objective` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`objective` text,
`target` varchar(140),
`uom` varchar(140),
`status` varchar(140) default 'Open',
`review` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:08,755 WARNING database DDL Query made to DB:
create table `tabQuality Meeting` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140) default 'Open',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:08,903 WARNING database DDL Query made to DB:
create table `tabQuality Feedback` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template` varchar(140),
`document_type` varchar(140),
`document_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:09,059 WARNING database DDL Query made to DB:
create table `tabQuality Goal` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`goal` varchar(140) unique,
`frequency` varchar(140) default 'None',
`procedure` varchar(140),
`weekday` varchar(140),
`date` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:09,228 WARNING database DDL Query made to DB:
create table `tabQuality Review` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`goal` varchar(140),
`date` date,
`procedure` varchar(140),
`status` varchar(140) default 'Open',
`additional_information` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:09,377 WARNING database DDL Query made to DB:
create table `tabQuality Procedure Process` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`process_description` longtext,
`procedure` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:09,494 WARNING database DDL Query made to DB:
create table `tabQuality Meeting Agenda` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`agenda` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:09,739 WARNING database DDL Query made to DB:
create table `tabCommunication Medium` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`communication_channel` varchar(140),
`communication_medium_type` varchar(140),
`catch_all` varchar(140),
`provider` varchar(140),
`disabled` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:09,890 WARNING database DDL Query made to DB:
create table `tabCommunication Medium Timeslot` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`day_of_week` varchar(140),
`from_time` time(6),
`to_time` time(6),
`employee_group` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:10,082 WARNING database DDL Query made to DB:
create table `tabCall Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`id` varchar(140) unique,
`from` varchar(140),
`to` varchar(140),
`call_received_by` varchar(140),
`employee_user_id` varchar(140),
`medium` varchar(140),
`start_time` datetime(6),
`end_time` datetime(6),
`type` varchar(140),
`customer` varchar(140),
`status` varchar(140),
`duration` decimal(21,9),
`recording_url` varchar(140),
`type_of_call` varchar(140),
`summary` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:10,252 WARNING database DDL Query made to DB:
create table `tabVoice Call Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140) unique,
`call_receiving_device` varchar(140) default 'Computer',
`greeting_message` varchar(140),
`agent_busy_message` varchar(140),
`agent_unavailable_message` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:10,402 WARNING database DDL Query made to DB:
create table `tabIncoming Call Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`call_routing` varchar(140) default 'Sequential',
`greeting_message` varchar(140),
`agent_busy_message` varchar(140),
`agent_unavailable_message` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:10,542 WARNING database DDL Query made to DB:
create table `tabTelephony Call Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`call_type` varchar(140) unique,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:10,687 WARNING database DDL Query made to DB:
create table `tabIncoming Call Handling Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`day_of_week` varchar(140),
`from_time` time(6) default '9:00:00',
`to_time` time(6) default '17:00:00',
`agent_group` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:10,843 WARNING database DDL Query made to DB:
create table `tabBulk Transaction Log Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`from_doctype` varchar(140),
`transaction_name` varchar(140),
`date` date,
`time` time(6),
`transaction_status` varchar(140),
`error_description` longtext,
`to_doctype` varchar(140),
`retried` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `from_doctype`(`from_doctype`),
index `transaction_name`(`transaction_name`),
index `date`(`date`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:11,251 WARNING database DDL Query made to DB:
create table `tabSubcontracting Receipt Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`is_scrap_item` int(1) not null default 0,
`description` longtext,
`brand` varchar(140),
`image` text,
`received_qty` decimal(21,9) not null default 0,
`qty` decimal(21,9) not null default 0,
`rejected_qty` decimal(21,9) not null default 0,
`returned_qty` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 1.0,
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`rm_cost_per_qty` decimal(21,9) not null default 0,
`service_cost_per_qty` decimal(21,9) not null default 0,
`additional_cost_per_qty` decimal(21,9) not null default 0,
`scrap_cost_per_qty` decimal(21,9) not null default 0,
`rm_supp_cost` decimal(21,9) not null default 0,
`warehouse` varchar(140),
`subcontracting_order` varchar(140),
`subcontracting_order_item` varchar(140),
`subcontracting_receipt_item` varchar(140),
`rejected_warehouse` varchar(140),
`bom` varchar(140),
`include_exploded_items` int(1) not null default 0,
`quality_inspection` varchar(140),
`schedule_date` date,
`reference_name` varchar(140),
`serial_and_batch_bundle` varchar(140),
`use_serial_batch_fields` int(1) not null default 0,
`rejected_serial_and_batch_bundle` varchar(140),
`serial_no` text,
`rejected_serial_no` text,
`batch_no` varchar(140),
`manufacturer` varchar(140),
`manufacturer_part_no` varchar(140),
`expense_account` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`page_break` int(1) not null default 0,
`purchase_order` varchar(140),
`purchase_order_item` varchar(140),
index `item_code`(`item_code`),
index `subcontracting_order`(`subcontracting_order`),
index `subcontracting_order_item`(`subcontracting_order_item`),
index `purchase_order`(`purchase_order`),
index `purchase_order_item`(`purchase_order_item`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:11,422 WARNING database DDL Query made to DB:
create table `tabSubcontracting BOM` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_active` int(1) not null default 1,
`finished_good` varchar(140),
`finished_good_qty` decimal(21,9) not null default 1.0,
`finished_good_uom` varchar(140),
`finished_good_bom` varchar(140),
`service_item` varchar(140),
`service_item_qty` decimal(21,9) not null default 1.0,
`service_item_uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `finished_good`(`finished_good`),
index `finished_good_bom`(`finished_good_bom`),
index `service_item`(`service_item`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:11,611 WARNING database DDL Query made to DB:
create table `tabSubcontracting Order Service Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`qty` decimal(21,9) not null default 0,
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`fg_item` varchar(140),
`fg_item_qty` decimal(21,9) not null default 1.0,
`purchase_order_item` varchar(140),
`material_request` varchar(140),
`material_request_item` varchar(140),
index `item_code`(`item_code`),
index `purchase_order_item`(`purchase_order_item`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:11,783 WARNING database DDL Query made to DB:
create table `tabSubcontracting Order Supplied Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`main_item_code` varchar(140),
`rm_item_code` varchar(140),
`stock_uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 1.0,
`reserve_warehouse` varchar(140),
`bom_detail_no` varchar(140),
`reference_name` varchar(140),
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`required_qty` decimal(21,9) not null default 0,
`supplied_qty` decimal(21,9) not null default 0,
`consumed_qty` decimal(21,9) not null default 0,
`returned_qty` decimal(21,9) not null default 0,
`total_supplied_qty` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:12,055 WARNING database DDL Query made to DB:
create table `tabSubcontracting Order` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) default '{supplier_name}',
`naming_series` varchar(140),
`purchase_order` varchar(140),
`supplier` varchar(140),
`supplier_name` varchar(140),
`supplier_warehouse` varchar(140),
`company` varchar(140),
`transaction_date` date,
`schedule_date` date,
`amended_from` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`set_warehouse` varchar(140),
`total_qty` decimal(21,9) not null default 0,
`total` decimal(21,9) not null default 0,
`set_reserve_warehouse` varchar(140),
`supplier_address` varchar(140),
`address_display` text,
`contact_person` varchar(140),
`contact_display` text,
`contact_mobile` text,
`contact_email` text,
`shipping_address` varchar(140),
`shipping_address_display` text,
`billing_address` varchar(140),
`billing_address_display` text,
`distribute_additional_costs_based_on` varchar(140) default 'Qty',
`total_additional_costs` decimal(21,9) not null default 0,
`status` varchar(140) default 'Draft',
`per_received` decimal(21,9) not null default 0,
`select_print_heading` varchar(140),
`letter_head` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `supplier`(`supplier`),
index `transaction_date`(`transaction_date`),
index `status`(`status`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:12,380 WARNING database DDL Query made to DB:
create table `tabSubcontracting Order Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`bom` varchar(140),
`include_exploded_items` int(1) not null default 0,
`schedule_date` date,
`expected_delivery_date` date,
`description` longtext,
`image` text,
`qty` decimal(21,9) not null default 1.0,
`received_qty` decimal(21,9) not null default 0,
`returned_qty` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 1.0,
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`rm_cost_per_qty` decimal(21,9) not null default 0,
`service_cost_per_qty` decimal(21,9) not null default 0,
`additional_cost_per_qty` decimal(21,9) not null default 0,
`warehouse` varchar(140),
`expense_account` varchar(140),
`manufacturer` varchar(140),
`manufacturer_part_no` varchar(140),
`material_request` varchar(140),
`material_request_item` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`purchase_order_item` varchar(140),
`page_break` int(1) not null default 0,
`subcontracting_conversion_factor` decimal(21,9) not null default 0,
`job_card` varchar(140),
index `item_code`(`item_code`),
index `expected_delivery_date`(`expected_delivery_date`),
index `material_request`(`material_request`),
index `material_request_item`(`material_request_item`),
index `purchase_order_item`(`purchase_order_item`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:12,681 WARNING database DDL Query made to DB:
create table `tabSubcontracting Receipt` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) default '{supplier_name}',
`naming_series` varchar(140),
`supplier` varchar(140),
`supplier_name` varchar(140),
`supplier_delivery_note` varchar(140),
`company` varchar(140),
`posting_date` date,
`posting_time` time(6),
`set_posting_time` int(1) not null default 0,
`is_return` int(1) not null default 0,
`return_against` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`set_warehouse` varchar(140),
`rejected_warehouse` varchar(140),
`supplier_warehouse` varchar(140),
`total_qty` decimal(21,9) not null default 0,
`total` decimal(21,9) not null default 0,
`in_words` varchar(240),
`bill_no` varchar(140),
`bill_date` date,
`supplier_address` varchar(140),
`contact_person` varchar(140),
`address_display` text,
`contact_display` text,
`contact_mobile` text,
`contact_email` text,
`shipping_address` varchar(140),
`shipping_address_display` text,
`billing_address` varchar(140),
`billing_address_display` text,
`distribute_additional_costs_based_on` varchar(140) default 'Qty',
`total_additional_costs` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`range` varchar(140),
`represents_company` varchar(140),
`status` varchar(140) default 'Draft',
`per_returned` decimal(21,9) not null default 0,
`auto_repeat` varchar(140),
`letter_head` varchar(140),
`language` varchar(140),
`instructions` text,
`select_print_heading` varchar(140),
`remarks` text,
`transporter_name` varchar(140),
`lr_no` varchar(140),
`lr_date` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `supplier`(`supplier`),
index `posting_date`(`posting_date`),
index `status`(`status`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:12,912 WARNING database DDL Query made to DB:
create table `tabSubcontracting Receipt Supplied Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`main_item_code` varchar(140),
`rm_item_code` varchar(140),
`item_name` varchar(140),
`bom_detail_no` varchar(140),
`description` longtext,
`stock_uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 1.0,
`reference_name` varchar(140),
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`available_qty_for_consumption` decimal(21,9) not null default 0,
`required_qty` decimal(21,9) not null default 0,
`consumed_qty` decimal(21,9) not null default 0,
`current_stock` decimal(21,9) not null default 0,
`serial_and_batch_bundle` varchar(140),
`use_serial_batch_fields` int(1) not null default 0,
`subcontracting_order` varchar(140),
`serial_no` text,
`batch_no` varchar(140),
`expense_account` varchar(140),
`cost_center` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:13,066 WARNING database DDL Query made to DB:
create table `tabCode List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`canonical_uri` varchar(140),
`url` varchar(140),
`default_common_code` varchar(140),
`version` varchar(140),
`publisher` varchar(140),
`publisher_id` varchar(140),
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:13,227 WARNING database DDL Query made to DB:
create table `tabCommon Code` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`code_list` varchar(140),
`title` varchar(300),
`common_code` varchar(300),
`description` text,
`additional_data` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `code_list`(`code_list`),
index `common_code`(`common_code`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:13,313 WARNING database DDL Query made to DB:
ALTER TABLE `tabCommon Code`
				ADD INDEX IF NOT EXISTS `code_list_common_code_index`(code_list, common_code)
2025-07-14 16:20:21,210 WARNING database DDL Query made to DB:
ALTER TABLE `tabAddress` ADD COLUMN `tax_category` varchar(140), ADD COLUMN `is_your_company_address` int(1) not null default 0
2025-07-14 16:20:21,375 WARNING database DDL Query made to DB:
ALTER TABLE `tabContact` ADD COLUMN `is_billing_contact` int(1) not null default 0
2025-07-14 16:20:22,629 WARNING database DDL Query made to DB:
create table `tabPurpose of Travel` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`purpose_of_travel` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:22,813 WARNING database DDL Query made to DB:
create table `tabAppraisal Goal` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`kra` text,
`per_weightage` decimal(21,9) not null default 0,
`score` decimal(21,9) not null default 0,
`score_earned` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:22,958 WARNING database DDL Query made to DB:
create table `tabEmployee Separation Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`company` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`employee_grade` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:23,104 WARNING database DDL Query made to DB:
create table `tabAppraisee` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`appraisal_template` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`branch` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:23,242 WARNING database DDL Query made to DB:
create table `tabTravel Request Costing` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`expense_type` varchar(140),
`sponsored_amount` decimal(21,9) not null default 0,
`funded_amount` decimal(21,9) not null default 0,
`total_amount` decimal(21,9) not null default 0,
`comments` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:23,384 WARNING database DDL Query made to DB:
create table `tabInterview Round` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`round_name` varchar(140) unique,
`interview_type` varchar(140),
`expected_average_rating` decimal(3,2),
`designation` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:23,623 WARNING database DDL Query made to DB:
create table `tabTraining Event Employee` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`status` varchar(140) default 'Open',
`attendance` varchar(140),
`is_mandatory` int(1) not null default 1,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:23,744 WARNING database DDL Query made to DB:
create table `tabOffer Term` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`offer_term` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:23,957 WARNING database DDL Query made to DB:
create table `tabJob Opening` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`job_title` varchar(140),
`designation` varchar(140),
`status` varchar(140),
`posted_on` datetime(6),
`closes_on` date,
`closed_on` date,
`company` varchar(140),
`department` varchar(140),
`employment_type` varchar(140),
`location` varchar(140),
`staffing_plan` varchar(140),
`planned_vacancies` int(11) not null default 0,
`job_requisition` varchar(140),
`vacancies` int(11) not null default 0,
`publish` int(1) not null default 0,
`route` varchar(140) unique,
`publish_applications_received` int(1) not null default 1,
`job_application_route` varchar(140),
`description` longtext,
`currency` varchar(140),
`lower_range` decimal(21,9) not null default 0,
`upper_range` decimal(21,9) not null default 0,
`salary_per` varchar(140) default 'Month',
`publish_salary_range` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:24,158 WARNING database DDL Query made to DB:
create table `tabLeave Allocation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`company` varchar(140),
`leave_type` varchar(140),
`from_date` date,
`to_date` date,
`new_leaves_allocated` decimal(21,9) not null default 0,
`carry_forward` int(1) not null default 0,
`unused_leaves` decimal(21,9) not null default 0,
`total_leaves_allocated` decimal(21,9) not null default 0,
`total_leaves_encashed` decimal(21,9) not null default 0,
`compensatory_request` varchar(140),
`leave_period` varchar(140),
`leave_policy` varchar(140),
`leave_policy_assignment` varchar(140),
`carry_forwarded_leaves_count` decimal(21,9) not null default 0,
`expired` int(1) not null default 0,
`amended_from` varchar(140),
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `employee_name`(`employee_name`),
index `leave_type`(`leave_type`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:24,312 WARNING database DDL Query made to DB:
create table `tabInterview Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:24,481 WARNING database DDL Query made to DB:
create table `tabLeave Policy` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:24,689 WARNING database DDL Query made to DB:
create table `tabEmployee Onboarding` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`job_applicant` varchar(140),
`job_offer` varchar(140),
`employee_onboarding_template` varchar(140),
`company` varchar(140),
`boarding_status` varchar(140) default 'Pending',
`project` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`employee_grade` varchar(140),
`holiday_list` varchar(140),
`date_of_joining` date,
`boarding_begins_on` date,
`notify_users_by_email` int(1) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:24,854 WARNING database DDL Query made to DB:
create table `tabEmployee Onboarding Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`company` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`employee_grade` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:25,063 WARNING database DDL Query made to DB:
create table `tabAttendance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`working_hours` decimal(21,9) not null default 0,
`status` varchar(140) default 'Present',
`leave_type` varchar(140),
`leave_application` varchar(140),
`attendance_date` date,
`company` varchar(140),
`department` varchar(140),
`attendance_request` varchar(140),
`half_day_status` varchar(140),
`shift` varchar(140),
`in_time` datetime(6),
`out_time` datetime(6),
`late_entry` int(1) not null default 0,
`early_exit` int(1) not null default 0,
`amended_from` varchar(140),
`modify_half_day_status` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `status`(`status`),
index `attendance_date`(`attendance_date`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:25,254 WARNING database DDL Query made to DB:
create table `tabStaffing Plan` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`department` varchar(140),
`from_date` date,
`to_date` date,
`total_estimated_budget` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:25,431 WARNING database DDL Query made to DB:
create table `tabJob Offer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`job_applicant` varchar(140),
`applicant_name` varchar(140),
`applicant_email` varchar(140),
`status` varchar(140),
`offer_date` date,
`designation` varchar(140),
`company` varchar(140),
`job_offer_term_template` varchar(140),
`select_terms` varchar(140),
`terms` longtext,
`letter_head` varchar(140),
`select_print_heading` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:25,583 WARNING database DDL Query made to DB:
create table `tabAppraisal KRA` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`kra` varchar(140),
`per_weightage` decimal(21,9) not null default 0,
`goal_completion` decimal(21,9) not null default 0,
`goal_score` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:25,716 WARNING database DDL Query made to DB:
create table `tabSkill` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`skill_name` varchar(140) unique,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:25,908 WARNING database DDL Query made to DB:
create table `tabLeave Block List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`leave_block_list_name` varchar(140) unique,
`company` varchar(140),
`applies_to_all_departments` int(1) not null default 0,
`leave_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:26,054 WARNING database DDL Query made to DB:
create table `tabDaily Work Summary` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`daily_work_summary_group` varchar(140),
`status` varchar(140) default 'Open',
`email_sent_to` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:26,228 WARNING database DDL Query made to DB:
create table `tabEmployee Boarding Activity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`activity_name` varchar(140),
`user` varchar(140),
`role` varchar(140),
`begin_on` int(11) not null default 0,
`duration` int(11) not null default 0,
`task` varchar(140),
`task_weight` decimal(21,9) not null default 0,
`required_for_employee_creation` int(1) not null default 0,
`description` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:26,368 WARNING database DDL Query made to DB:
create table `tabLeave Block List Date` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`block_date` date,
`reason` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:26,515 WARNING database DDL Query made to DB:
create table `tabExpected Skill Set` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`skill` varchar(140),
`description` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:26,710 WARNING database DDL Query made to DB:
create table `tabEmployee Checkin` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`log_type` varchar(140),
`shift` varchar(140),
`time` datetime(6),
`device_id` varchar(140),
`skip_auto_attendance` int(1) not null default 0,
`attendance` varchar(140),
`latitude` decimal(21,9) not null default 0,
`longitude` decimal(21,9) not null default 0,
`geolocation` longtext,
`shift_start` datetime(6),
`shift_end` datetime(6),
`offshift` int(1) not null default 0,
`shift_actual_start` datetime(6),
`shift_actual_end` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `shift`(`shift`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:27,013 WARNING database DDL Query made to DB:
create table `tabExpense Claim` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`company` varchar(140),
`expense_approver` varchar(140),
`approval_status` varchar(140) default 'Draft',
`total_sanctioned_amount` decimal(21,9) not null default 0,
`total_taxes_and_charges` decimal(21,9) not null default 0,
`total_advance_amount` decimal(21,9) not null default 0,
`grand_total` decimal(21,9) not null default 0,
`total_claimed_amount` decimal(21,9) not null default 0,
`total_amount_reimbursed` decimal(21,9) not null default 0,
`posting_date` date,
`is_paid` int(1) not null default 0,
`mode_of_payment` varchar(140),
`payable_account` varchar(140),
`clearance_date` date,
`remark` text,
`project` varchar(140),
`cost_center` varchar(140),
`status` varchar(140) default 'Draft',
`task` varchar(140),
`amended_from` varchar(140),
`delivery_trip` varchar(140),
`vehicle_log` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `approval_status`(`approval_status`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:27,251 WARNING database DDL Query made to DB:
create table `tabEmployee Advance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`posting_date` date,
`company` varchar(140),
`department` varchar(140),
`currency` varchar(140),
`exchange_rate` decimal(21,9) not null default 0,
`purpose` text,
`advance_amount` decimal(21,9) not null default 0,
`paid_amount` decimal(21,9) not null default 0,
`pending_amount` decimal(21,9) not null default 0,
`claimed_amount` decimal(21,9) not null default 0,
`return_amount` decimal(21,9) not null default 0,
`advance_account` varchar(140),
`mode_of_payment` varchar(140),
`repay_unclaimed_amount_from_salary` int(1) not null default 0,
`status` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:27,492 WARNING database DDL Query made to DB:
create table `tabAppraisal` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`employee_image` text,
`company` varchar(140),
`appraisal_cycle` varchar(140),
`start_date` date,
`end_date` date,
`final_score` decimal(21,9) not null default 0,
`appraisal_template` varchar(140),
`rate_goals_manually` int(1) not null default 0,
`goal_score_percentage` decimal(21,9) not null default 0,
`remarks` text,
`total_score` decimal(21,9) not null default 0,
`avg_feedback_score` decimal(21,9) not null default 0,
`self_score` decimal(21,9) not null default 0,
`reflections` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:27,720 WARNING database DDL Query made to DB:
create table `tabFull and Final Statement` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`transaction_date` date,
`company` varchar(140),
`status` varchar(140) default 'Unpaid',
`amended_from` varchar(140),
`date_of_joining` date,
`relieving_date` date,
`designation` varchar(140),
`department` varchar(140),
`total_asset_recovery_cost` decimal(21,9) not null default 0,
`total_payable_amount` decimal(21,9) not null default 0,
`total_receivable_amount` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:27,880 WARNING database DDL Query made to DB:
create table `tabAppraisal Template Goal` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`key_result_area` varchar(140),
`per_weightage` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:28,067 WARNING database DDL Query made to DB:
create table `tabExpense Taxes and Charges` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`account_head` varchar(140),
`rate` decimal(21,9) not null default 0,
`tax_amount` decimal(21,9) not null default 0,
`total` decimal(21,9) not null default 0,
`description` text,
`cost_center` varchar(140),
`project` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:28,487 WARNING database DDL Query made to DB:
create table `tabLeave Policy Assignment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`company` varchar(140),
`leave_policy` varchar(140),
`carry_forward` int(1) not null default 0,
`assignment_based_on` varchar(140),
`leave_period` varchar(140),
`effective_from` date,
`effective_to` date,
`leaves_allocated` int(1) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:28,640 WARNING database DDL Query made to DB:
create table `tabLeave Policy Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`leave_type` varchar(140),
`annual_allocation` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:28,785 WARNING database DDL Query made to DB:
create table `tabDaily Work Summary Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enabled` int(1) not null default 1,
`send_emails_at` varchar(140),
`holiday_list` varchar(140),
`subject` varchar(140) default 'What did you work on today?',
`message` longtext default '<p>Please share what did you do today. If you reply by midnight, your response will be recorded!</p>',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:28,926 WARNING database DDL Query made to DB:
create table `tabInterview Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`interviewer` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:29,112 WARNING database DDL Query made to DB:
create table `tabAppraisal Cycle` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`cycle_name` varchar(140) unique,
`company` varchar(140),
`status` varchar(140) default 'Not Started',
`start_date` date,
`end_date` date,
`description` longtext,
`kra_evaluation_method` varchar(140) default 'Automated Based on Goal Progress',
`calculate_final_score_based_on_formula` int(1) not null default 0,
`final_score_formula` longtext,
`branch` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-14 16:20:29,343 WARNING database DDL Query made to DB:
create table `tabTraining Event` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`event_name` varchar(140) unique,
`training_program` varchar(140),
`event_status` varchar(140),
`has_certificate` int(1) not null default 0,
`type` varchar(140),
`level` varchar(140),
`company` varchar(140),
`trainer_name` varchar(140),
`trainer_email` varchar(140),
`supplier` varchar(140),
`contact_number` varchar(140),
`course` varchar(140),
`location` varchar(140),
`start_time` datetime(6),
`end_time` datetime(6),
`introduction` longtext,
`amended_from` varchar(140),
`employee_emails` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
