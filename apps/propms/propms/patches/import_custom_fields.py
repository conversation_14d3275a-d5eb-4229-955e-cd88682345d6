import frappe

def execute():
    custom_field_names = [
        "Company-default_maintenance_tax_template",
        "Company-default_tax_account_head",
        "Company-default_tax_template",
        "Company-property_management_settings",
        "Company-security_account_code",
        "Issue Materials Detail-mateiral_request",
        "Issue-col_brk_001",
        "Issue-column_break_14",
        "Issue-column_break_4",
        "Issue-customer_feedback",
        "Issue-defect_found",
        "Issue-material_request",
        "Issue-materials_billed",
        "Issue-materials_required",
        "Issue-person_in_charge_name",
        "Issue-person_in_charge",
        "Issue-property_name",
        "Issue-section_break_15",
        "Issue-sub_contractor_contact",
        "Issue-sub_contractor_name",
        "Item-reading_required",
        "Material Request Item-material_request",
        "Material Request-sales_invoice",
        "Quotation-cost_center",
        "Sales Invoice-job_card",
        "Sales Invoice-lease_information",
        "Sales Invoice-lease_item",
        "Sales Invoice-lease",
    ]
    for name in custom_field_names:
        try:
            doc = frappe.get_doc("Custom Field", name)
            doc.insert(ignore_if_duplicate=True)
        except frappe.DoesNotExistError:
            frappe.logger().warning(f"Custom Field {name} not found for patch.")
