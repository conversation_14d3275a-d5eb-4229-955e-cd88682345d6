import frappe

def execute():
    property_setter_names = [
        "Contact-department-fieldtype",
        "Contact-department-options",
        "Daily Checklist-default_print_format",
        "Issue-company-fetch_from",
        "Issue-email_account-report_hide",
        "Issue-issue_type-in_standard_filter",
        "Issue-issue_type-reqd",
        "Issue-opening_date-in_list_view",
        "Issue-priority-in_standard_filter",
        "Issue-quick_entry",
        "Issue-raised_by-in_list_view",
        "Issue-raised_by-report_hide",
        "Issue-section_break_19-collapsible",
        "Issue-section_break_7-collapsible",
        "Issue-status-options",
        "Journal Entry Account-account-columns",
        "Journal Entry Account-cost_center-columns",
        "Key Set Detail-key_set-in_standard_filter",
        "Key Set Detail-returned-in_standard_filter",
        "Key Set Detail-taken_by-in_standard_filter",
        "Lease-customer-in_list_view",
        "Lease-customer-in_standard_filter",
        "Lease-end_date-in_list_view",
        "Lease-lease_customer-in_standard_filter",
        "Lease-property_owner-in_list_view",
        "Lease-property_owner-in_standard_filter",
        "Lease-property_user-in_standard_filter",
        "Lease-property-in_standard_filter",
        "Lease-security_status-in_standard_filter",
        "Lease-security_status-options",
        "Lease-start_date-in_list_view",
        "Lease-wtax_paid_by-in_standard_filter",
        "Material Request-material_request_type-options",
        "Property-identification_section-bold",
        "Property-section_break_13-bold",
        "Property-section_break_22-bold",
        "Property-section_break_4-bold",
        "Property-status-options",
        "Unit Type-search_fields",
    ]
    for name in property_setter_names:
        try:
            doc = frappe.get_doc("Property Setter", name)
            doc.insert(ignore_if_duplicate=True)
        except frappe.DoesNotExistError:
            frappe.logger().warning(f"Property Setter {name} not found for patch.")
